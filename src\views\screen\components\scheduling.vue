<script setup lang="ts">
import { getCurrentInstance, ref, onMounted, onUnmounted, computed } from "vue";
import CommonLayout from '@/components/CommonLayout.vue';
import * as XLSX from 'xlsx';
const { proxy } = getCurrentInstance() as any;

// 图表实例
const chartRef = ref();
let chartInstance: any = null;

// 表格数据
const tableData = ref<any[]>([]);

// 模型配置
// 初始化时间：开始时间为当前时间（只保留小时），结束时间为当前小时往后推24小时
const initializeDateTime = () => {
  const now = new Date();
  // 设置开始时间为当前时间，只保留小时（分钟和秒设为0）
  const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), 0, 0);
  // 设置结束时间为开始时间往后推24小时
  const endTime = new Date(startTime);
  endTime.setHours(endTime.getHours() + 24);

  // 格式化为 YYYY-MM-DD HH:mm 格式
  const formatDateTime = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };

  return {
    start: formatDateTime(startTime),
    end: formatDateTime(endTime)
  };
};

const { start: initialStartDate, end: initialEndDate } = initializeDateTime();
const startDate = ref(initialStartDate);
const endDate = ref(initialEndDate);
const activeTab = ref('sanxia'); // 'sanxia' 或 'gezhouba'

// 文件上传相关
const uploadRef = ref();
const fileList = ref([]);
const excelData = ref({
  FH1_sx: [],  // 第一列数据 - 三峡负荷计划
  FH1_gzb: [], // 第二列数据 - 葛洲坝负荷计划
  Qin1_sx: [], // 第三列数据 - 三峡入库流量
  qujian: []   // 第四列数据 - 区间入流
});
const loadPlanFile = ref(''); // 共享的负荷计划文件名
const isLoading = ref(false); // 加载状态
const hasUploadedFile = ref(false); // 是否已上传文件

// xlsx库已在顶部导入

// 处理Excel文件读取
const readExcelFile = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target?.result;
        if (!arrayBuffer) {
          throw new Error('无法读取文件内容');
        }

        // 使用xlsx库解析Excel文件
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });

        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // 将工作表转换为JSON对象数组
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        console.log('读取Excel文件:', file.name, jsonData);

        // 提取前四列数据
        const FH1_sx: number[] = [];  // 第一列 - 三峡负荷计划
        const FH1_gzb: number[] = []; // 第二列 - 葛洲坝负荷计划
        const Qin1_sx: number[] = []; // 第三列 - 三峡入库流量
        const qujian: number[] = [];  // 第四列 - 区间入流

        // 遍历每一行，提取数据
        // 跳过第一行（标题行）
        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i] as any[];
          if (row && row.length >= 4) {
            // 确保数据是数字类型
            FH1_sx.push(Number(row[0]) || 0);
            FH1_gzb.push(Number(row[1]) || 0);
            Qin1_sx.push(Number(row[2]) || 0);
            qujian.push(Number(row[3]) || 0);
          }
        }

        // 返回提取的数据
        const extractedData = {
          FH1_sx,
          FH1_gzb,
          Qin1_sx,
          qujian
        };

        console.log('提取的Excel数据:', extractedData);
        resolve(extractedData);
      } catch (error) {
        console.error('解析Excel文件失败:', error);
        reject(error);
      }
    };
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
};

// 三峡配置参数
const sanxiaParams = ref({
  loadPlan: '', // 负荷计划文件
  // inflow: 6000, // 入库流量
  minOutflow: 6000, // 最小下泄流量
  waterLevelUpper: 175, // 水位上限
  waterLevelLower: 153, // 水位下限
  initialWaterLevel: 155.93 // 初始水位
});

// 葛洲坝配置参数
const gezhoubaParams = ref({
  loadPlan: '', // 负荷计划文件
  // intervalInflow: 500, // 区间入流
  minOutflow: 5700, // 最小下泄流量
  waterLevelUpper: 67, // 水位上限
  waterLevelLower: 62, // 水位下限
  initialWaterLevel: 66.19 // 初始水位
});

// 当前显示的结果类型
const resultType = ref('sanxia'); // 'sanxia' 或 'gezhouba'

// 生成模拟数据
function generateMockData() {
  const hours = 24;
  const data = [];
  const baseTime = new Date('2025-04-18 00:00');

  // 生成每6小时一个数据点
  for (let i = 0; i <= hours; i += 6) {
    const time = new Date(baseTime);
    time.setHours(time.getHours() + i);

    // 格式化时间
    const timeStr = time.toISOString().replace('T', ' ').substring(0, 16);

    // 三峡数据
    const sanxiaWaterHead = 99.85;
    const sanxiaWaterLevel = 165.0 - Math.sin(i / 24 * Math.PI) * 0.5;
    const sanxiaOutflowRate = 6000;

    // 葛洲坝数据
    const gezhoubaWaterHead = 30.5;
    const gezhoubaWaterLevel = 65.0 - Math.sin((i + 2) / 24 * Math.PI) * 0.3;
    const gezhoubaOutflowRate = 6000;

    data.push({
      time: timeStr,
      sanxia: {
        // 负荷计划保留整数
        loadPlan: 5500,
        loadPlanDisplay: '5500',
        // 出力保留整数
        output: 5400,
        outputDisplay: '5400',
        // 水头保留一位小数
        waterHead: sanxiaWaterHead,
        waterHeadDisplay: sanxiaWaterHead.toFixed(1),
        // 水位保留两位小数
        waterLevel: sanxiaWaterLevel,
        waterLevelDisplay: sanxiaWaterLevel.toFixed(2),
        // 出库流量保留整数
        outflowRate: sanxiaOutflowRate,
        outflowRateDisplay: sanxiaOutflowRate.toString()
      },
      gezhouba: {
        // 负荷计划保留整数
        loadPlan: 5500,
        loadPlanDisplay: '5500',
        // 出力保留整数
        output: 5400,
        outputDisplay: '5400',
        // 水头保留一位小数
        waterHead: gezhoubaWaterHead,
        waterHeadDisplay: gezhoubaWaterHead.toFixed(1),
        // 水位保留两位小数
        waterLevel: gezhoubaWaterLevel,
        waterLevelDisplay: gezhoubaWaterLevel.toFixed(2),
        // 出库流量保留整数
        outflowRate: gezhoubaOutflowRate,
        outflowRateDisplay: gezhoubaOutflowRate.toString()
      }
    });
  }

  return data;
}

// 计算当前显示的表格数据
const displayTableData = computed(() => {
  if (!tableData.value || tableData.value.length === 0) {
    return [];
  }

  return tableData.value.map(item => {
    const currentData = item[resultType.value];
    return {
      time: item.time,
      // 负荷计划保留整数
      loadPlan: currentData.loadPlanDisplay || Math.round(currentData.loadPlan).toString(),
      // 出力保留整数
      output: currentData.outputDisplay || Math.round(currentData.output).toString(),
      // 水头保留一位小数
      waterHead: currentData.waterHeadDisplay || currentData.waterHead.toFixed(1),
      // 水位保留两位小数
      waterLevel: currentData.waterLevelDisplay || currentData.waterLevel.toFixed(2),
      // 出库流量保留整数
      outflowRate: currentData.outflowRateDisplay || Math.round(currentData.outflowRate).toString()
    };
  });
});

// 初始化图表
function initChart() {
  chartInstance = proxy.$echarts.init(chartRef.value, 'dark-blue');
  const initOption = {};
  chartInstance.setOption(initOption);
}

// 更新图表数据
function updateChart() {
  if (!tableData.value || tableData.value.length === 0) {
    return;
  }

  // 提取时间和数据系列，确保所有数值保留1位小数
  const times = tableData.value.map((item: any) => item.time);
  const outflowData = tableData.value.map((item: any) => item[resultType.value].outflowRate); // 已经在processResponseData中处理为1位小数
  const waterLevelData = tableData.value.map((item: any) => item[resultType.value].waterLevel); // 已经在processResponseData中处理为1位小数

  // 自定义颜色数组，确保颜色差异明显
  const customColors = [
    '#ff6464', // 红色
    '#4b9afa', // 蓝色
  ];

  const option = {
    title: {
      text: resultType.value === 'sanxia' ? '三峡调度结果' : '葛洲坝调度结果',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#00ffff'
      },
      top: '20px',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params: any[]) {
        let result = params[0].name + '<br/>';
        params.forEach((param: any) => {
          // 出库流量保留整数，水位保留两位小数
          let value;
          if (param.seriesName === '出库流量') {
            value = Math.round(param.value).toString();
          } else {
            value = param.value.toFixed(2);
          }
          result += param.marker + ' ' + param.seriesName + ': ' + value +
                    (param.seriesName === '出库流量' ? ' m³/s' : ' m') + '<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['出库流量', '水位'],
      textStyle: {
        color: '#ffffff',
        fontSize: 14
      },
      top: '50px'
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '80px',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: times,
      axisLabel: {
        fontSize: 14,
        color: '#ffffff'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '出库流量(m³/s)',
        position: 'left',
        nameTextStyle: {
          fontSize: 14,
          color: '#ffffff'
        },
        axisLine: {
          lineStyle: {
            color: customColors[0]
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        axisLabel: {
          formatter: function(value: number) {
            // 出库流量轴保留整数
            return Math.round(value).toString();
          },
          fontSize: 14,
          color: '#ffffff'
        },
        scale: true, // 不从零开始，根据数据自适应
        // min: function(value) {
        //   // 设置最小值为数据最小值的95%，留出一些空间
        //   return Math.floor(value.min * 0.95);
        // },
        // max: function(value) {
        //   // 设置最大值为数据最大值的105%，留出一些空间
        //   return Math.ceil(value.max * 1.05);
        // }
      },
      {
        type: 'value',
        name: '水位(m)',
        position: 'right',
        nameTextStyle: {
          fontSize: 14,
          color: '#ffffff'
        },
        axisLine: {
          lineStyle: {
            color: customColors[1]
          }
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: function(value: number) {
            // 水位轴保留两位小数
            return value.toFixed(2);
          },
          fontSize: 14,
          color: '#ffffff'
        },
        scale: true, // 不从零开始，根据数据自适应
        // min: function(value: any) {
        //   // 设置最小值为数据最小值的99.5%，留出一些空间但保持水位变化明显
        //   return (value.min * 0.995).toFixed(1);
        // },
        // max: function(value: any) {
        //   // 设置最大值为数据最大值的100.5%，留出一些空间但保持水位变化明显
        //   return (value.max * 1.005).toFixed(1);
        // }
      }
    ],
    color: customColors,
    series: [
      {
        name: '出库流量',
        type: 'line',
        yAxisIndex: 0,
        data: outflowData,
        lineStyle: {
          width: 3,
          shadowColor: 'rgba(255, 100, 100, 0.5)',
          shadowBlur: 10
        },
        symbol: 'circle',
        symbolSize: 8,
        smooth: true,
        areaStyle: {
          opacity: 0.2,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(255, 100, 100, 0.5)'
            }, {
              offset: 1, color: 'rgba(255, 100, 100, 0)'
            }]
          }
        }
      },
      {
        name: '水位',
        type: 'line',
        yAxisIndex: 1,
        data: waterLevelData,
        lineStyle: {
          width: 3,
          shadowColor: 'rgba(75, 154, 250, 0.5)',
          shadowBlur: 10
        },
        symbol: 'circle',
        symbolSize: 8,
        smooth: true
      }
    ]
  };

  chartInstance.setOption(option);
}

// 切换结果类型
function switchResultType(type: string) {
  resultType.value = type;
  updateChart();
}

// 切换配置选项卡
function switchConfigTab(tab: string) {
  activeTab.value = tab;
}

// 上传负荷计划文件
function uploadLoadPlan() {
  // 创建一个隐藏的文件输入元素
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = '.xlsx,.xls,.csv';
  fileInput.style.display = 'none';

  // 监听文件选择事件
  fileInput.addEventListener('change', async (event) => {
    const files = (event.target as HTMLInputElement).files;
    if (files && files.length > 0) {
      const file = files[0];
      // 保存文件名 - 三峡和葛洲坝共享同一个负荷计划文件
      loadPlanFile.value = file.name;
      sanxiaParams.value.loadPlan = file.name;
      gezhoubaParams.value.loadPlan = file.name;

      try {
        // 读取Excel文件数据
        const fileData = await readExcelFile(file);
        // 保存读取的数据
        excelData.value = fileData as any;
        hasUploadedFile.value = true;
        console.log('Excel数据已加载:', excelData.value);
      } catch (error) {
        console.error('读取Excel文件失败:', error);
      }
    }
  });

  // 触发文件选择对话框
  document.body.appendChild(fileInput);
  fileInput.click();
  document.body.removeChild(fileInput);
}

// 生成时间序列（每15分钟一个点）
function generateTimeSequence(startTime: string, endTime: string): string[] {
  const times: string[] = [];
  const start = new Date(startTime + ":00");
  const end = new Date(endTime + ":00");

  const current = new Date(start);
  while (current <= end) {
    const timeStr = current.toISOString().replace('T', ' ').substring(0, 19);
    times.push(timeStr);
    current.setMinutes(current.getMinutes() + 15); // 每15分钟一个点
  }

  return times;
}

// 构建请求数据
function buildRequestData() {
  const timeSequence = generateTimeSequence(startDate.value, endDate.value);

  // 构建基础参数
  const basicParams = {
    st_time: startDate.value + ":00",
    end_time: endDate.value + ":00",
    qst_sx: sanxiaParams.value.minOutflow,
    qst_gzb: gezhoubaParams.value.minOutflow,
    zupper_sx: sanxiaParams.value.waterLevelUpper,
    zupper_gzb: gezhoubaParams.value.waterLevelUpper,
    zlower_sx: sanxiaParams.value.waterLevelLower,
    zlower_gzb: gezhoubaParams.value.waterLevelLower,
    z01_sx: sanxiaParams.value.initialWaterLevel,
    z01_gzb: gezhoubaParams.value.initialWaterLevel
  };

  // 构建运行数据集
  const runDataSet: any = {
    FH: [],
    qujian: [],
    Qin1_sx: [],
    sx_xhl: [],
    gzb_xhl: []
  };

  // 处理负荷计划数据 (FH)
  // 三峡负荷计划
  excelData.value.FH1_sx.forEach((value: number, index: number) => {
    if (index < timeSequence.length) {
      runDataSet.FH.push(["sx", timeSequence[index], value]);
    }
  });

  // 葛洲坝负荷计划
  excelData.value.FH1_gzb.forEach((value: number, index: number) => {
    if (index < timeSequence.length) {
      runDataSet.FH.push(["gzb", timeSequence[index], value]);
    }
  });

  // 处理区间入流数据 (qujian)
  excelData.value.qujian.forEach((value: number, index: number) => {
    if (index < timeSequence.length) {
      runDataSet.qujian.push([value, timeSequence[index]]);
    }
  });

  // 处理三峡入库流量数据 (Qin1_sx)
  excelData.value.Qin1_sx.forEach((value: number, index: number) => {
    if (index < timeSequence.length) {
      runDataSet.Qin1_sx.push([value, timeSequence[index]]);
    }
  });

  // 处理泄洪数据 (sx_xhl 和 gzb_xhl) - 默认为0
  timeSequence.forEach((time: string) => {
    runDataSet.sx_xhl.push([0, time]);
    runDataSet.gzb_xhl.push([0, time]);
  });

  return {
    basicParams,
    runDataSet
  };
}

// 存储请求数据到param.json文件
async function saveRequestDataToParamFile(requestData: any) {
  try {
    console.log('开始存储请求数据到param.json文件...');

    // 发送请求到后端保存文件
    const saveResponse = await fetch('/api/save-param', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        data: requestData,
        filePath: 'D:\\Work\\三峡web\\sanxia_backend\\init\\param.json'
      })
    });

    if (!saveResponse.ok) {
      throw new Error(`保存文件失败: ${saveResponse.status}`);
    }

    console.log('param.json文件保存成功');

    // 通知后端文件存储完成
    await notifyParamFileComplete();

  } catch (error) {
    console.error('保存param.json文件失败:', error);
    // 即使保存失败，也不影响主流程，只记录错误
    throw error; // 重新抛出错误，让调用方知道失败了
  }
}

// 通知后端param.json文件存储完成
async function notifyParamFileComplete() {
  try {
    console.log('通知后端param.json文件存储完成...');

    const notifyResponse = await fetch('/api/param/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: 'completed',
        message: 'param.json文件存储完成',
        timestamp: new Date().toISOString(),
        filePath: 'D:\\Work\\三峡web\\sanxia_backend\\init\\param.json'
      })
    });

    if (!notifyResponse.ok) {
      throw new Error(`通知后端失败: ${notifyResponse.status}`);
    }

    const notifyResult = await notifyResponse.json();
    console.log('后端通知成功:', notifyResult);

  } catch (error) {
    console.error('通知后端失败:', error);
    // 通知失败不影响主流程
  }
}

// 执行计算
async function calculate() {
  try {
    // 检查是否已上传文件
    if (!hasUploadedFile.value) {
      alert('请先上传负荷计划文件');
      return;
    }

    isLoading.value = true;

    // 构建请求数据 - 按照新的格式要求
    const requestData = buildRequestData();

    console.log('构建的请求数据:', requestData);

    // 存储请求数据到param.json文件，然后通知后端
    try {
      // 存储请求数据到param.json文件
      await saveRequestDataToParamFile(requestData);

      // 使用模拟数据进行界面显示（因为不再直接调用计算接口）
      console.log('使用模拟数据进行界面显示...');
      const mockData = generateMockData();
      tableData.value = mockData;
      updateChart();
    } catch (error) {
      console.error('存储param.json文件失败:', error);
      // 如果存储失败，仍然使用模拟数据
      console.log('使用模拟数据...');
      const mockData = generateMockData();
      tableData.value = mockData;
      updateChart();
    }
  } finally {
    isLoading.value = false;
  }
}

// 定义响应数据类型
interface ResponseData {
  qt_sxls: number[]; // 三峡出库流量
  Nt_sxls: number[]; // 三峡出力
  FH1_sx: number[]; // 三峡负荷计划
  FH1_gzb: number[]; // 葛洲坝负荷计划
  zt_next_sxls: number[]; // 三峡水位
  shuitou_sxls: number[]; // 三峡水头
  qt_gzbls: number[]; // 葛洲坝出库流量
  Nt_gzbls: number[]; // 葛洲坝出力
  zt_next_gzbls: number[]; // 葛洲坝水位
  shuitou_gzbls: number[]; // 葛洲坝水头
  time_list: string[]; // 时间序列
}

// 处理响应数据
function processResponseData(data: ResponseData) {
  // 解构响应数据
  const {
    qt_sxls, // 三峡出库流量
    Nt_sxls, // 三峡出力
    FH1_sx, // 三峡负荷计划
    FH1_gzb, // 葛洲坝负荷计划
    zt_next_sxls, // 三峡水位
    shuitou_sxls, // 三峡水头
    qt_gzbls, // 葛洲坝出库流量
    Nt_gzbls, // 葛洲坝出力
    zt_next_gzbls, // 葛洲坝水位
    shuitou_gzbls, // 葛洲坝水头
    time_list // 时间序列
  } = data;

  // 转换数据为表格格式，按要求设置不同的小数位数
  const processedData = time_list.map((time: string, index: number) => {
    return {
      time,
      sanxia: {
        // 负荷计划保留整数
        loadPlan: Math.round(FH1_sx[index]),
        loadPlanDisplay: Math.round(FH1_sx[index]).toString(),
        // 出力保留整数
        output: Math.round(Nt_sxls[index]),
        outputDisplay: Math.round(Nt_sxls[index]).toString(),
        // 水头保留一位小数
        waterHead: Number(shuitou_sxls[index].toFixed(1)),
        waterHeadDisplay: shuitou_sxls[index].toFixed(1),
        // 水位保留两位小数
        waterLevel: Number(zt_next_sxls[index].toFixed(2)),
        waterLevelDisplay: zt_next_sxls[index].toFixed(2),
        // 出库流量保留整数
        outflowRate: Math.round(qt_sxls[index]),
        outflowRateDisplay: Math.round(qt_sxls[index]).toString()
      },
      gezhouba: {
        // 负荷计划保留整数
        loadPlan: Math.round(FH1_gzb[index]),
        loadPlanDisplay: Math.round(FH1_gzb[index]).toString(),
        // 出力保留整数
        output: Math.round(Nt_gzbls[index]),
        outputDisplay: Math.round(Nt_gzbls[index]).toString(),
        // 水头保留一位小数
        waterHead: Number(shuitou_gzbls[index].toFixed(1)),
        waterHeadDisplay: shuitou_gzbls[index].toFixed(1),
        // 水位保留两位小数
        waterLevel: Number(zt_next_gzbls[index].toFixed(2)),
        waterLevelDisplay: zt_next_gzbls[index].toFixed(2),
        // 出库流量保留整数
        outflowRate: Math.round(qt_gzbls[index]),
        outflowRateDisplay: Math.round(qt_gzbls[index]).toString()
      }
    };
  });

  // 更新表格数据
  tableData.value = processedData;

  // 更新图表
  updateChart();
}

// 屏幕适配
function screenAdapter() {
  chartInstance?.resize();
}

onMounted(() => {
  initChart();
  // 不再自动调用calculate()，等待用户点击计算按钮
  window.addEventListener('resize', screenAdapter);
  screenAdapter();
});

onUnmounted(() => {
  window.removeEventListener('resize', screenAdapter);
  chartInstance?.dispose();
});
</script>

<template>
  <CommonLayout>
    <div class="container">
      <div class="page-title">三峡-葛洲坝梯级短期调度</div>
      <div class="content-container">
        <!-- 左侧配置区域 -->
        <div class="config-panel">
          <div class="panel-title">模型配置</div>

          <!-- 调度起止时间 -->
          <div class="config-section">
            <div class="section-title">调度起止时间</div>
            <div class="time-range">
              <div class="time-input">
                <el-date-picker
                  v-model="startDate"
                  type="datetime"
                  placeholder="选择开始时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm"
                  class="date-picker"
                />
              </div>
              <span class="time-separator">至</span>
              <div class="time-input">
                <el-date-picker
                  v-model="endDate"
                  type="datetime"
                  placeholder="选择结束时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm"
                  class="date-picker"
                />
              </div>
            </div>
          </div>

          <!-- 电站选择 -->
          <div class="station-tabs-container">
            <div class="station-tabs">
              <div
                class="station-tab"
                :class="{ active: activeTab === 'sanxia' }"
                @click="switchConfigTab('sanxia')"
              >
                三峡
              </div>
              <div
                class="station-tab"
                :class="{ active: activeTab === 'gezhouba' }"
                @click="switchConfigTab('gezhouba')"
              >
                葛洲坝
              </div>
            </div>
          </div>

          <!-- 三峡配置 -->
          <div v-if="activeTab === 'sanxia'" class="station-config">
            <div class="config-item">
              <div class="config-label">负荷计划</div>
              <div class="config-value upload-container">
                <button class="upload-btn" @click="uploadLoadPlan">导入本地文件</button>
                <div v-if="loadPlanFile" class="file-name">{{ loadPlanFile }}</div>
              </div>
            </div>
<!--<div class="config-item">
              <div class="config-label">入库流量</div>
              <div class="config-value">
                <input type="number" v-model="sanxiaParams.inflow" class="custom-input" />
              </div>
            </div>  -->


            <div class="config-item">
              <div class="config-label">最小下泄流量</div>
              <div class="config-value">
                <input type="number" v-model="sanxiaParams.minOutflow" class="custom-input" />
              </div>
            </div>

            <div class="config-item">
              <div class="config-label">水位上限</div>
              <div class="config-value">
                <input type="number" v-model="sanxiaParams.waterLevelUpper" class="custom-input" />
              </div>
            </div>

            <div class="config-item">
              <div class="config-label">水位下限</div>
              <div class="config-value">
                <input type="number" v-model="sanxiaParams.waterLevelLower" class="custom-input" />
              </div>
            </div>

            <div class="config-item">
              <div class="config-label">初始水位</div>
              <div class="config-value">
                <input type="number" v-model="sanxiaParams.initialWaterLevel" class="custom-input" />
              </div>
            </div>
          </div>

          <!-- 葛洲坝配置 -->
          <div v-if="activeTab === 'gezhouba'" class="station-config">
            <div class="config-item">
              <div class="config-label">负荷计划</div>
              <div class="config-value upload-container">
                <button class="upload-btn" @click="uploadLoadPlan">导入本地文件</button>
                <div v-if="loadPlanFile" class="file-name">{{ loadPlanFile }}</div>
              </div>
            </div>
<!-- <div class="config-item">
              <div class="config-label">区间入流</div>
              <div class="config-value">
                <input type="number" v-model="gezhoubaParams.intervalInflow" class="custom-input" />
              </div>
            </div> -->


            <div class="config-item">
              <div class="config-label">最小下泄流量</div>
              <div class="config-value">
                <input type="number" v-model="gezhoubaParams.minOutflow" class="custom-input" />
              </div>
            </div>

            <div class="config-item">
              <div class="config-label">水位上限</div>
              <div class="config-value">
                <input type="number" v-model="gezhoubaParams.waterLevelUpper" class="custom-input" />
              </div>
            </div>

            <div class="config-item">
              <div class="config-label">水位下限</div>
              <div class="config-value">
                <input type="number" v-model="gezhoubaParams.waterLevelLower" class="custom-input" />
              </div>
            </div>

            <div class="config-item">
              <div class="config-label">初始水位</div>
              <div class="config-value">
                <input type="number" v-model="gezhoubaParams.initialWaterLevel" class="custom-input" />
              </div>
            </div>
          </div>

          <!-- 计算按钮 -->
          <div class="calculate-btn-container">
            <button class="calculate-btn" @click="calculate" :disabled="isLoading">
              {{ isLoading ? '计算中...' : '计算' }}
            </button>
          </div>
        </div>

        <!-- 右侧结果区域 -->
        <div class="result-panel">
          <!-- 加载遮罩 -->
          <div v-if="isLoading" class="loading-overlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">数据计算中，请稍候...</div>
          </div>

          <!-- 图表区域 -->
          <div class="chart-container">
            <div class="chart-wrapper" ref="chartRef"></div>
          </div>

          <!-- 表格区域 -->
          <div class="table-container">
            <!-- 表格标签切换 -->
            <div class="result-tabs">
              <div
                class="result-tab"
                :class="{ active: resultType === 'sanxia' }"
                @click="switchResultType('sanxia')"
              >
                三峡
              </div>
              <div
                class="result-tab"
                :class="{ active: resultType === 'gezhouba' }"
                @click="switchResultType('gezhouba')"
              >
                葛洲坝
              </div>
            </div>

            <!-- 结果表格 -->
            <div class="table-wrapper">
              <el-table
                :data="displayTableData"
                class="custom-table"
                :header-cell-style="{backgroundColor: 'rgba(13, 34, 82, 0.8)', color: '#00ffff', fontWeight: 'bold', fontSize: '15px'}"
                :row-style="{backgroundColor: 'rgba(10, 26, 58, 0.6)'}"
                :cell-style="{color: '#ffffff', fontSize: '14px'}"
                :bg-color="'transparent'"
                table-layout="fixed"
                size="small"
                height="100%"
                style="width: 100%;"
              >
                <el-table-column prop="time" label="时间" min-width="150" align="center" />
                <el-table-column prop="loadPlan" label="负荷计划(MW)" min-width="120" align="center" />
                <el-table-column prop="output" label="出力(MW)" min-width="120" align="center" />
                <el-table-column prop="waterHead" label="水头(m)" min-width="100" align="center" />
                <el-table-column prop="waterLevel" label="水位(m)" min-width="100" align="center" />
                <el-table-column prop="outflowRate" label="出库流量(m³/s)" min-width="140" align="center" />
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </CommonLayout>
</template>

<style lang="scss" scoped>
// 主题颜色变量
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$data-highlight: #00ffff;

/* 修复表格右侧白色区域 */
:deep(.el-table) {
  background-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-bg-color: transparent;
  --el-table-border-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__inner-wrapper::before) {
  display: none;
}

:deep(.el-table__fixed-right) {
  background-color: rgba(10, 26, 58, 0.6);
  height: 100% !important;
}

:deep(.el-table__fixed-right-patch) {
  background-color: rgba(13, 34, 82, 0.8);
}

:deep(.el-table__cell) {
  background-color: transparent;
}

:deep(.el-table--border::after),
:deep(.el-table--border::before),
:deep(.el-table__inner-wrapper::before) {
  background-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__fixed::before),
:deep(.el-table__fixed-right::before) {
  background-color: transparent;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: calc(100vh - 120px); /* 减去头部和导航的高度 */
  color: $text-light;
  overflow-y: auto;
  // padding: 10px;
  padding: 0px;
  box-sizing: border-box;
}

.page-title {
  font-size: 26px;
  font-weight: bold;
  color: $text-highlight;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
  margin-bottom: 20px;
  text-align: center;
  width: 100%;
}

.content-container {
  display: flex;
  width: 100%;
  height: calc(100% - 50px);
  gap: 20px;
}

// 左侧配置面板
.config-panel {
  width: 530px;
  background-color: rgba($secondary-dark-blue, 0.7);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 0 20px rgba($glow-blue, 0.2);
  display: flex;
  flex-direction: column;
}

.panel-title {
  color: $text-highlight;
  font-size: 18px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba($border-glow, 0.3);
  padding-bottom: 8px;
  text-align: center;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
}

.config-section {
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  color: $text-light;
  margin-bottom: 10px;
  text-align: center;
}

.time-range {
  display: flex;
  align-items: center;
  justify-content: center; /* 居中显示 */
  gap: 15px; /* 使用gap代替space-between，更好控制间距 */
}

.time-input {
  position: relative;
  flex: 1;
  // max-width: 190px; /* 限制时间输入框的最大宽度 */
  width: 190px; /* 限制时间输入框的最大宽度 */
}

.time-separator {
  margin: 0 10px;
  color: $text-light;
  font-size: 15px;
}

.radio-circle {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid $highlight-blue;
  background-color: transparent;
}

.custom-input {
  width: 100%;
  padding: 8px;
  background-color: rgba($secondary-dark-blue, 0.5);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 4px;
  color: $text-light;
  font-size: 15px;
  text-align: center; /* 内容居中显示 */

  &:focus {
    outline: none;
    border-color: $highlight-blue;
    box-shadow: 0 0 5px rgba($highlight-blue, 0.5);
  }
}

// 日期选择器样式
.date-picker {
  width: 100%;
  --el-input-bg-color: rgba(10, 26, 58, 0.6);
  --el-input-text-color: #ffffff;
  --el-input-border-color: rgba(0, 136, 255, 0.5);
  --el-input-hover-border-color: rgba(0, 168, 255, 0.8);
}

:deep(.el-input__wrapper) {
  background-color: rgba(10, 26, 58, 0.6);
  box-shadow: 0 0 0 1px rgba(0, 136, 255, 0.5) inset;
}

:deep(.el-input__inner) {
  color: #ffffff;
  font-size: 14px;
  // text-align: center; /* 内容居中显示 */
}

// 修改日期选择器弹出框样式
:deep(.el-picker__popper) {
  background-color: rgba(13, 34, 82, 0.95) !important;
  border: 1px solid rgba(0, 168, 255, 0.5) !important;
  box-shadow: 0 0 15px rgba(0, 198, 255, 0.3) !important;
}

:deep(.el-date-picker) {
  --el-datepicker-text-color: #ffffff;
  --el-datepicker-off-text-color: rgba(255, 255, 255, 0.6);
  --el-datepicker-header-text-color: #00ffff;
  --el-datepicker-icon-color: #00a8ff;
  --el-datepicker-border-color: rgba(0, 168, 255, 0.3);
  --el-datepicker-inner-border-color: rgba(0, 168, 255, 0.3);
  --el-datepicker-inrange-bg-color: rgba(0, 168, 255, 0.1);
  --el-datepicker-inrange-hover-bg-color: rgba(0, 168, 255, 0.2);
  --el-datepicker-active-color: #00a8ff;
  --el-datepicker-hover-text-color: #00ffff;
}

:deep(.el-picker-panel__icon-btn) {
  color: #00a8ff !important;
}

:deep(.el-date-table td.current:not(.disabled)) span {
  background-color: #00a8ff !important;
  color: #ffffff !important;
}

:deep(.el-date-table td.today span) {
  color: #00ffff !important;
}

:deep(.el-date-table td:hover) {
  color: #00ffff !important;
}

:deep(.el-time-panel) {
  background-color: rgba(13, 34, 82, 0.95) !important;
  border-color: rgba(0, 168, 255, 0.3) !important;
}

:deep(.el-time-panel__content::before),
:deep(.el-time-panel__content::after) {
  border-color: rgba(0, 168, 255, 0.3) !important;
}

:deep(.el-time-spinner__item:hover:not(.disabled):not(.active)) {
  background: rgba(0, 168, 255, 0.1) !important;
  color: #00ffff !important;
}

:deep(.el-time-spinner__item.active:not(.disabled)) {
  color: #00ffff !important;
  font-weight: bold;
}

:deep(.el-picker-panel__footer .el-button) {
  color: #ffffff !important;
  border-color: rgba(0, 168, 255, 0.5) !important;
  background-color: rgba(0, 168, 255, 0.2) !important;
}

:deep(.el-picker-panel__footer .el-button--primary) {
  background-color: #00a8ff !important;
  border-color: #00a8ff !important;
  color: #ffffff !important;
}

// 电站选择标签
.station-tabs-container {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.station-tabs {
  display: flex;
  border-bottom: 1px solid rgba($border-glow, 0.3);
  width: 80%;
}

.station-tab {
  padding: 8px 15px;
  cursor: pointer;
  color: $text-light;
  font-size: 16px;
  transition: all 0.3s;
  flex: 1;
  text-align: center;

  &.active {
    color: $text-highlight;
    border-bottom: 2px solid $highlight-blue;
  }

  &:hover:not(.active) {
    color: darken($text-highlight, 10%);
  }
}

// 电站配置
.station-config {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 15px;
  min-height: 300px; // 固定高度，防止切换时抖动
}

.config-item {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}

.config-label {
  width: 120px;
  color: $text-light;
  font-size: 15px;
}

.config-value {
  flex: 1;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-btn {
  padding: 8px 15px;
  background-color: $highlight-blue;
  border: none;
  border-radius: 4px;
  color: $text-light;
  cursor: pointer;
  font-size: 15px;
  transition: all 0.3s;

  &:hover {
    background-color: darken($highlight-blue, 10%);
  }
}

.file-name {
  margin-top: 5px;
  font-size: 14px;
  color: $text-light;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 计算按钮
.calculate-btn-container {
  margin-top: 20px; /* 改为固定的上边距，而不是自动占满剩余空间 */
  text-align: center;
}

.calculate-btn {
  padding: 10px 30px;
  background-color: $highlight-blue;
  border: none;
  border-radius: 4px;
  color: $text-light;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: darken($highlight-blue, 10%);
    box-shadow: 0 0 10px rgba($highlight-blue, 0.5);
  }
}

// 右侧结果面板
.result-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative; /* 为加载遮罩提供定位上下文 */
}

// 加载遮罩
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba($secondary-dark-blue, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: 5px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba($border-glow, 0.3);
  border-radius: 50%;
  border-top-color: $highlight-blue;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: $text-highlight;
  font-size: 18px;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
}

// 图表区域
.chart-container {
  height: 50%;
  background-color: rgba($secondary-dark-blue, 0.7);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 0 20px rgba($glow-blue, 0.2);
}

.chart-wrapper {
  width: 100%;
  height: 100%;
}

// 表格区域
.table-container {
  height: 50%;
  background-color: rgba($secondary-dark-blue, 0.7);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 0 20px rgba($glow-blue, 0.2);
  display: flex;
  flex-direction: column;
}

// 结果标签切换
.result-tabs {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba($border-glow, 0.3);
  justify-content: center;
}

.result-tab {
  padding: 8px 15px;
  cursor: pointer;
  color: $text-light;
  font-size: 16px;
  transition: all 0.3s;
  min-width: 100px;
  text-align: center;

  &.active {
    color: $text-highlight;
    border-bottom: 2px solid $highlight-blue;
  }

  &:hover:not(.active) {
    color: darken($text-highlight, 10%);
  }
}

.table-wrapper {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-table {
  --el-table-border-color: rgba(0, 136, 255, 0.3);
  --el-table-border: 1px solid var(--el-table-border-color);
  --el-table-text-color: #ffffff;
  --el-table-header-text-color: #00ffff;
  --el-table-row-hover-bg-color: rgba(0, 168, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  width: auto;
  margin: 0 auto;
  font-size: 14px;
}

// 适配深色模式下的Element Plus组件
:deep(.el-input-number) {
  --el-input-bg-color: rgba(10, 26, 58, 0.6);
  --el-input-text-color: #ffffff;
  --el-input-border-color: rgba(0, 136, 255, 0.5);
  --el-input-hover-border-color: rgba(0, 168, 255, 0.8);
  width: 100%;
  height: 32px;
  font-size: 14px;
}

:deep(.el-input__wrapper) {
  background-color: rgba(10, 26, 58, 0.6);
  box-shadow: 0 0 0 1px rgba(0, 136, 255, 0.5) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px rgba(0, 198, 255, 0.8) inset;
}

:deep(.el-input__inner) {
  color: #ffffff;
  background-color: transparent;
  font-size: 14px;
  // text-align: center; /* 内容居中显示 */
}
</style>
