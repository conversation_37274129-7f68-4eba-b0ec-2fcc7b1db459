<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <div class="logo">
          <img src="/static/img/长江电力.png" alt="Logo" />
        </div>
        <h1 class="title">实时动库容对三峡-葛洲坝梯级短期调度影响</h1>
      </div>

      <div class="login-form">
        <h2 class="form-title">系统登录</h2>

        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" status-icon>
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="login-footer">
        <p>© 长江电力. 保留所有权利.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="LoginPage">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, FormInstance } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/userStore'

// 路由
const router = useRouter()

// 用户Store
const userStore = useUserStore()

// 登录表单引用
const loginFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 准备表单数据
        const formData = new URLSearchParams()
        formData.append('grant_type', 'password')
        formData.append('username', loginForm.username)
        formData.append('password', loginForm.password)
        formData.append('client_id', 'test_client')

        // 发送OAuth2请求
        const response = await fetch('http://localhost:8000/api/oauth/token/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: formData
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error_description || '认证失败')
        }

        // 解析响应
        const tokenResponse = await response.json()

        // 保存用户信息和令牌
        userStore.setUserInfo({
          username: loginForm.username,
          access_token: tokenResponse.access_token,
          token_type: tokenResponse.token_type,
          refresh_token: tokenResponse.refresh_token,
          expires_in: tokenResponse.expires_in
        })

        // 登录成功提示
        ElMessage({
          type: 'success',
          message: '登录成功'
        })

        // 跳转到首页
        router.push('/home')
      } catch (error) {
        console.error('登录失败:', error)
        ElMessage({
          type: 'error',
          message: error instanceof Error ? error.message : '登录失败，请检查用户名和密码'
        })
      } finally {
        loading.value = false
      }
    }
  })
}

// 组件挂载时检查是否已登录
onMounted(() => {
  // 加载本地存储的用户信息
  userStore.loadUserInfo()

  // 如果已登录，直接跳转到首页
  if (userStore.userInfo.isLoggedIn) {
    router.push('/home')
  }
})
</script>

<style lang="scss" scoped>
// 主题颜色
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;

.login-container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $primary-dark-blue;
  background-image: linear-gradient(to bottom, $secondary-dark-blue, $primary-dark-blue);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/static/img/grid-pattern.png');
    background-size: 20px 20px;
    opacity: 0.1;
    pointer-events: none;
    z-index: 1;
  }
}

.login-box {
  width: 450px;
  padding: 30px;
  background-color: rgba($secondary-dark-blue, 0.7);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 10px;
  box-shadow: 0 0 20px rgba($glow-blue, 0.2);
  position: relative;
  z-index: 10;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;

  .logo {
    margin-bottom: 15px;

    img {
      height: 60px;
    }
  }

  .title {
    color: $text-light;
    font-size: 26px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba($glow-blue, 0.5);
    letter-spacing: 2px;
    margin: 0;
  }
}

.login-form {
  margin-bottom: 30px;

  .form-title {
    color: $text-highlight;
    font-size: 18px;
    margin-bottom: 20px;
    text-align: center;
    border-bottom: 1px solid rgba($border-glow, 0.3);
    padding-bottom: 10px;
  }

  .login-button {
    width: 100%;
    background-color: $highlight-blue;
    border-color: $highlight-blue;

    &:hover {
      background-color: lighten($highlight-blue, 10%);
      border-color: lighten($highlight-blue, 10%);
      box-shadow: 0 0 15px rgba($glow-blue, 0.5);
    }
  }
}

.login-footer {
  text-align: center;
  color: rgba($text-light, 0.6);
  font-size: 12px;
}

// 自定义Element Plus样式
:deep(.el-input__wrapper) {
  background-color: rgba($primary-dark-blue, 0.5);
  box-shadow: 0 0 0 1px rgba($border-glow, 0.3);

  &:hover, &.is-focus {
    box-shadow: 0 0 0 1px $highlight-blue;
  }
}

:deep(.el-input__inner) {
  color: $text-light;

  &::placeholder {
    color: rgba($text-light, 0.5);
  }
}

:deep(.el-input__prefix-inner) {
  color: rgba($text-light, 0.7);
}
</style>
