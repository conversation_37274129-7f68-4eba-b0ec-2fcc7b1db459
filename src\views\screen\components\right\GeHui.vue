<script setup lang="ts">
import { getCurrentInstance, ref, onMounted, onUnmounted } from "vue";
import CommonLayout from '@/components/CommonLayout.vue';
// import Sangehuishui2 from '@/views/screen/components/right/Sangehuishui2.vue';
const { proxy } = getCurrentInstance() as any;

// 获取html元素
const trend_ref1 = ref();
const trend_ref2 = ref();
let chartInstance1: any = null;
let chartInstance2: any = null;
// 移除未使用的变量

const arr = [40, 42, 44, 46, 48, 50, 52];
const arr2 = [10000, 20000, 30000, 40000, 50000, 60000];
const tableDataArr = [
  [40.21, 42.11, 44.06, 46.04, 48.02, 50.02, 52.01],
  [40.79, 42.43, 44.25, 46.15, 48.09, 50.06, 52.04],
  [41.61, 42.93, 44.56, 46.33, 48.21, 50.13, 52.08],
  [42.55, 43.56, 44.96, 46.58, 48.36, 50.23, 52.15],
  [43.53, 44.27, 45.44, 46.89, 48.56, 50.36, 52.23],
  [44.49, 45.03, 45.98, 47.25, 48.8, 50.51, 52.33]
];

const tableData = [
  { flow: '10000', '40': '40.21', '42': '42.11', '44': '44.06', '46': '46.04', '48': '48.02', '50': '50.02', '52': '52.01', range: '1-21cm' },
  { flow: '20000', '40': '40.79', '42': '42.43', '44': '44.25', '46': '46.15', '48': '48.09', '50': '50.06', '52': '52.04', range: '4-79cm' },
  { flow: '30000', '40': '41.61', '42': '42.93', '44': '44.56', '46': '46.33', '48': '48.21', '50': '50.13', '52': '52.08', range: '8-161cm' },
  { flow: '40000', '40': '42.55', '42': '43.56', '44': '44.96', '46': '46.58', '48': '48.36', '50': '50.23', '52': '52.15', range: '15-255cm' },
  { flow: '50000', '40': '43.53', '42': '44.27', '44': '45.44', '46': '46.89', '48': '48.56', '50': '50.36', '52': '52.23', range: '23-353cm' },
  { flow: '60000', '40': '44.49', '42': '45.03', '44': '45.98', '46': '47.25', '48': '48.8', '50': '50.51', '52': '52.33', range: '33-449cm' },
];

import { useThemeStore } from '@/stores/theme';
const themeStore = useThemeStore();

themeStore.$subscribe(() => {
  chartInstance1?.dispose();
  chartInstance2?.dispose();
  initChart1();
  initChart2();
  screenAdapter();
  updateChart1();
  updateChart2();
});

onMounted(() => {
  initChart1();
  initChart2();
  getData();
  window.addEventListener('resize', screenAdapter);
  screenAdapter();
});

onUnmounted(() => {
  window.removeEventListener('resize', screenAdapter);
  chartInstance1?.dispose();
  chartInstance2?.dispose();
});

// 初始化第一个图表
function initChart1() {
  // 初始化图表
  chartInstance1 = proxy.$echarts.init(trend_ref1.value, 'dark-blue');
  const initOption = {};
  chartInstance1.setOption(initOption);
}

// 初始化第二个图表
function initChart2() {
  // 初始化图表
  chartInstance2 = proxy.$echarts.init(trend_ref2.value, 'dark-blue');
  const initOption = {};
  chartInstance2.setOption(initOption);
}

// 获取数据
async function getData() {
  // 更新图表数据
  updateChart1();
  updateChart2();
}

// 更新第一个图表
function updateChart1() {
  const dataOption = {
    title: {
      text: '顶托水位(m)',
      textStyle: { fontSize: 16, fontWeight: 'bold', color: '#00ffff' },
      top: '20px'
    },
    tooltip: { trigger: 'axis' },
    legend: {
      data: ["10000", "20000", "30000", "40000", "50000", "60000"],
      textStyle: {
        fontSize: 14,
        color: '#ffffff'
      }
    },
    grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: { fontSize: 14 },
      data: ["40", "42", "44", "46", "48", "50", "52"]
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 14 },
      min: 39,
      max: 53
    },
    // 自定义颜色数组，确保颜色差异明显
    color: [
      '#00c6ff', // 亮蓝色
      '#ff9500', // 橙色
      '#00ff95', // 绿色
      '#ff00c8', // 粉色
      '#ffff00', // 黄色
      '#9d00ff'  // 紫色
    ],
    series: [
      { name: "10000", type: 'line', smooth: true, data: [tableData[0]["40"], tableData[0]["42"], tableData[0]["44"], tableData[0]["46"], tableData[0]["48"], tableData[0]["50"], tableData[0]["52"]] },
      { name: "20000", type: 'line', smooth: true, data: [tableData[1]["40"], tableData[1]["42"], tableData[1]["44"], tableData[1]["46"], tableData[1]["48"], tableData[1]["50"], tableData[1]["52"]] },
      { name: "30000", type: 'line', smooth: true, data: [tableData[2]["40"], tableData[2]["42"], tableData[2]["44"], tableData[2]["46"], tableData[2]["48"], tableData[2]["50"], tableData[2]["52"]] },
      { name: "40000", type: 'line', smooth: true, data: [tableData[3]["40"], tableData[3]["42"], tableData[3]["44"], tableData[3]["46"], tableData[3]["48"], tableData[3]["50"], tableData[3]["52"]] },
      { name: "50000", type: 'line', smooth: true, data: [tableData[4]["40"], tableData[4]["42"], tableData[4]["44"], tableData[4]["46"], tableData[4]["48"], tableData[4]["50"], tableData[4]["52"]] },
      { name: "60000", type: 'line', smooth: true, data: [tableData[5]["40"], tableData[5]["42"], tableData[5]["44"], tableData[5]["46"], tableData[5]["48"], tableData[5]["50"], tableData[5]["52"]] }
    ]
  };
  chartInstance1.setOption(dataOption);
}

// 更新第二个图表
function updateChart2() {
  const dataOption = {
    title: {
      text: '顶托水位(m)',
      textStyle: { fontSize: 16, fontWeight: 'bold', color: '#00ffff' },
      top: '20px'
    },
    tooltip: { trigger: 'axis' },
    legend: {
      data: ["40", "42", "44", "46", "48", "50", "52"],
      textStyle: {
        fontSize: 14,
        color: '#ffffff'
      }
    },
    grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: { fontSize: 14 },
      data: ["10000", "20000", "30000", "40000", "50000", "60000"]
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 14 },
      min: 39,
      max: 53
    },
    // 自定义颜色数组，确保颜色差异明显
    color: [
      '#00c6ff', // 亮蓝色
      '#ff9500', // 橙色
      '#00ff95', // 绿色
      '#ff00c8', // 粉色
      '#ffff00', // 黄色
      '#9d00ff'  // 紫色
    ],
    series: [
      { name: "40", type: 'line', smooth: true, data: [tableData[0]["40"], tableData[1]["40"], tableData[2]["40"], tableData[3]["40"], tableData[4]["40"], tableData[5]["40"]] },
      { name: "42", type: 'line', smooth: true, data: [tableData[0]["42"], tableData[1]["42"], tableData[2]["42"], tableData[3]["42"], tableData[4]["42"], tableData[5]["42"]] },
      { name: "44", type: 'line', smooth: true, data: [tableData[0]["44"], tableData[1]["44"], tableData[2]["44"], tableData[3]["44"], tableData[4]["44"], tableData[5]["44"]] },
      { name: "46", type: 'line', smooth: true, data: [tableData[0]["46"], tableData[1]["46"], tableData[2]["46"], tableData[3]["46"], tableData[4]["46"], tableData[5]["46"]] },
      { name: "48", type: 'line', smooth: true, data: [tableData[0]["48"], tableData[1]["48"], tableData[2]["48"], tableData[3]["48"], tableData[4]["48"], tableData[5]["48"]] },
      { name: "50", type: 'line', smooth: true, data: [tableData[0]["50"], tableData[1]["50"], tableData[2]["50"], tableData[3]["50"], tableData[4]["50"], tableData[5]["50"]] },
      { name: "52", type: 'line', smooth: true, data: [tableData[0]["52"], tableData[1]["52"], tableData[2]["52"], tableData[3]["52"], tableData[4]["52"], tableData[5]["52"]] }
    ]
  };
  chartInstance2.setOption(dataOption);
}

// 屏幕适配
function screenAdapter() {
  chartInstance1?.resize();
  chartInstance2?.resize();
}

// 计算相关
const num1 = ref(40);
const num2 = ref(10000);
const result = ref(40.21);

function bilinearInterpolation(x: any, y: any, data: any) {
  let id1 = Math.floor((num1.value - 40) / 2);
  let id2 = Math.floor((num1.value - 40) / 2) + 1;
  let id3 = Math.floor((num2.value - 10000) / 10000);
  let id4 = Math.floor((num2.value - 10000) / 10000) + 1;
  let x1 = arr[id1];
  let x2 = arr[id2];
  let y1 = arr2[id3];
  let y2 = arr2[id4];
  let R1 = (x2 - x) / (x2 - x1) * data[id3][id1] + (x - x1) / (x2 - x1) * data[id3][id2];
  let R2 = (x2 - x) / (x2 - x1) * data[id4][id1] + (x - x1) / (x2 - x1) * data[id4][id2];
  let Z = (y2 - y) / (y2 - y1) * R1 + (y - y1) / (y2 - y1) * R2;
  return Z;
}

function queryData() {
  result.value = bilinearInterpolation(num1.value, num2.value, tableDataArr);
}

function formatNoDecimal(value: any) {
  return value.toFixed(2);
}

defineExpose({
  onMounted,
  updateChart1,
  updateChart2,
  screenAdapter
});
</script>

<template>
  <CommonLayout>
    <div class="container">
      <div class="page-title">葛洲坝回水顶托计算</div>
      <div class="table-container">
        <div class="table-panel">
          <div class="panel-title">顶托水位数据表</div>
          <div class="table-wrapper">
            <el-table
              :data="tableData"
              style="max-height: 280px; overflow-y: auto;"
              class="custom-table"
              :header-cell-style="{backgroundColor: 'rgba(13, 34, 82, 0.8)', color: '#00ffff', fontWeight: 'bold', fontSize: '15px'}"
              :row-style="{backgroundColor: 'rgba(10, 26, 58, 0.6)'}"
              :cell-style="{color: '#ffffff', fontSize: '14px'}"
              :bg-color="'transparent'"
              table-layout="fixed"
              size="small"
            >
              <el-table-column prop="flow" label="流量(m³/s)" width="120" align="center" fixed/>
              <el-table-column label="水位(m)" align="center">
                <el-table-column prop="40" label="40" width="80" align="center"/>
                <el-table-column prop="42" label="42" width="80" align="center"/>
                <el-table-column prop="44" label="44" width="80" align="center"/>
                <el-table-column prop="46" label="46" width="80" align="center"/>
                <el-table-column prop="48" label="48" width="80" align="center"/>
                <el-table-column prop="50" label="50" width="80" align="center"/>
                <el-table-column prop="52" label="52" width="80" align="center"/>
                <el-table-column prop="range" label="范围" width="100" align="center"/>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="query-panel">
          <div class="panel-title">顶托水位查询</div>
          <div class="query-form">
            <div class="input-row">
              <div class="form-item">
                <el-text class="label">水位(m)：</el-text>
                <el-input-number
                  v-model="num1"
                  :precision="2"
                  :step="0.1"
                  :min="40"
                  :max="51.99"
                  class="custom-input"
                  size="small"
                />
              </div>
              <div class="form-item">
                <el-text class="label">流量(m³/s)：</el-text>
                <el-input-number
                  v-model="num2"
                  :precision="0"
                  :step="10000"
                  :min="10000"
                  :max="59999"
                  class="custom-input"
                  size="small"
                />
              </div>
            </div>
            <el-button type="primary" @click="queryData" class="query-btn">查询</el-button>

            <div class="result-container">
              <div class="result-label">计算结果 (m)：</div>
              <div class="result-value">
                <el-statistic :value="result" :formatter="formatNoDecimal" class="custom-statistic">
                </el-statistic>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="chart-container">
        <div class="chart-panel">
          <div class="com-container">
            <div class="com-chart1" ref="trend_ref1"></div>
          </div>
        </div>
        <div class="chart-panel">
          <div class="com-container">
            <div class="com-chart1" ref="trend_ref2"></div>
          </div>
        </div>
      </div>
    </div>
  </CommonLayout>
</template>

<style lang="scss" scoped>
// Main colors
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$data-highlight: #00ffff;

/* 修复表格右侧白色区域 */
:deep(.el-table) {
  background-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-bg-color: transparent;
  --el-table-border-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__inner-wrapper::before) {
  display: none;
}

:deep(.el-table__fixed-right) {
  background-color: rgba(10, 26, 58, 0.6);
  height: 100% !important;
}

:deep(.el-table__fixed-right-patch) {
  background-color: rgba(13, 34, 82, 0.8);
}

:deep(.el-table__cell) {
  background-color: transparent;
}

:deep(.el-table--border::after),
:deep(.el-table--border::before),
:deep(.el-table__inner-wrapper::before) {
  background-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__fixed::before),
:deep(.el-table__fixed-right::before) {
  background-color: transparent;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: calc(100vh - 120px); /* 减去头部和导航的高度 */
  color: $text-light;
  overflow-y: auto;
}

.page-title {
  font-size: 26px;
  font-weight: bold;
  color: $text-highlight;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
  margin-bottom: 20px;
  text-align: center;
  width: 100%;
}

.table-container {
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.table-panel, .query-panel, .chart-panel {
  background-color: rgba($secondary-dark-blue, 0.7);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 5px;
  box-shadow: 0 0 20px rgba($glow-blue, 0.2);
  padding: 15px;
}

.table-panel {
  width: 65%;
  height: 380px;
  display: flex;
  flex-direction: column;
}

.table-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  overflow: hidden;
  padding: 0 10px;
}

.query-panel {
  width: 35%;
  height: 380px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-title {
  color: $text-highlight;
  font-size: 18px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba($border-glow, 0.3);
  padding-bottom: 8px;
  text-align: center;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
}

.custom-table {
  --el-table-border-color: rgba(0, 136, 255, 0.3);
  --el-table-border: 1px solid var(--el-table-border-color);
  --el-table-text-color: #ffffff;
  --el-table-header-text-color: #00ffff;
  --el-table-row-hover-bg-color: rgba(0, 168, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  width: auto;
  margin: 0 auto;
  font-size: 14px;
}

.query-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

.input-row {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  gap: 10px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 48%;
}

.label {
  color: $text-light;
  font-size: 15px;
}

.custom-input {
  width: 100%;
  margin-top: 10px;
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-text-color: #ffffff;
  --el-input-border-color: rgba(0, 136, 255, 0.5);
  --el-input-hover-border-color: rgba(0, 168, 255, 0.8);
}

.query-btn {
  margin-top: 15px;
  background-color: $highlight-blue;
  border-color: $highlight-blue;
  color: $text-light;
  font-weight: bold;
  width: 100%;
  font-size: 15px;
  height: 36px;
  &:hover {
    background-color: darken($highlight-blue, 10%);
    border-color: darken($highlight-blue, 10%);
  }
}

.result-container {
  margin-top: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba($border-glow, 0.5);
  border-radius: 5px;
  padding: 8px;
}

.result-label {
  font-size: 15px;
  color: $text-highlight;
  margin-bottom: 3px;
  text-align: center;
}

.result-value {
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-statistic {
  --el-statistic-content-font-size: 32px;
  --el-statistic-content-color: $data-highlight;
}

.chart-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 20px;
  margin-bottom: 20px; /* 添加底部边距，防止内容超出边界 */
}

.chart-panel {
  width: 48%;
  height: 280px; /* 减小高度，防止超出边界 */
}

.com-container {
  width: 100%;
  height: 100%;
}

.com-chart1 {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// 适配深色模式下的Element Plus组件
:deep(.el-input-number) {
  --el-input-bg-color: rgba(10, 26, 58, 0.6);
  --el-input-text-color: #ffffff;
  --el-input-border-color: rgba(0, 136, 255, 0.5);
  --el-input-hover-border-color: rgba(0, 168, 255, 0.8);
  width: 100%;
  height: 32px;
  font-size: 14px;
}

:deep(.el-input__wrapper) {
  background-color: rgba(10, 26, 58, 0.6);
  box-shadow: 0 0 0 1px rgba(0, 136, 255, 0.5) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px rgba(0, 198, 255, 0.8) inset;
}

:deep(.el-input__inner) {
  color: #ffffff;
  background-color: transparent;
  font-size: 14px;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background-color: rgba(13, 34, 82, 0.8);
  color: #00ffff;
  border-color: rgba(0, 136, 255, 0.5);
}
</style>