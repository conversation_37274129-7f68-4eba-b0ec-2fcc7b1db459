<script setup lang="ts">
import {getCurrentInstance, ref, onMounted, onUnmounted} from "vue";
import CommonLayout from '@/components/CommonLayout.vue';
const { proxy } = getCurrentInstance() as any;

//获取html元素
const trend_ref1 = ref()
const trend_ref2 = ref();
let chartInstance1: any = null  //应该是对象
let chartInstance2: any = null;

const arr = [62, 62.2, 62.4, 62.6, 62.8, 63, 63.2, 63.4, 63.6, 63.8, 64, 64.2, 64.4, 64.6, 64.8, 65, 65.2, 65.4, 65.6, 65.8, 66, 66.2, 66.4, 66.6, 66.8, 67]
const arr2 = [4000, 6000, 8000, 10000, 12000, 14000, 16000, 18000, 20000, 22000, 24000, 26000, 28000, 30000, 32000, 34000, 36000, 38000, 40000, 42000, 44000, 46000, 48000, 50000, 52000, 54000, 56000, 58000, 60000, 62000, 64000, 66000, 68000, 70000, 72000, 74000]

//创建一个二维数组，用来存放数据
const tableDataArr = [
  [5.983,6.036,6.089,6.143,6.197,6.252,6.306,6.361,6.417,6.472,6.528,6.584,6.64,6.697,6.753,6.811,6.868,6.926,6.984,7.041,7.1,7.158,7.217,7.276,7.334,7.393],
  [5.984,6.036,6.089,6.143,6.197,6.252,6.306,6.361,6.417,6.472,6.528,6.584,6.64,6.697,6.754,6.811,6.868,6.926,6.984,7.042,7.1,7.159,7.217,7.276,7.335,7.394],
  [5.985,6.037,6.09,6.144,6.198,6.253,6.307,6.362,6.418,6.473,6.529,6.585,6.641,6.698,6.755,6.812,6.869,6.927,6.985,7.043,7.101,7.16,7.218,7.277,7.336,7.395],
  [5.986,6.039,6.092,6.146,6.2,6.255,6.309,6.364,6.419,6.474,6.53,6.586,6.642,6.699,6.756,6.813,6.87,6.928,6.986,7.044,7.102,7.161,7.219,7.278,7.337,7.396],
  [5.988,6.04,6.093,6.147,6.201,6.256,6.31,6.365,6.421,6.476,6.532,6.588,6.644,6.701,6.758,6.815,6.872,6.93,6.988,7.046,7.104,7.162,7.221,7.279,7.338,7.397],
  [5.99,6.042,6.095,6.149,6.203,6.258,6.312,6.367,6.423,6.478,6.534,6.59,6.646,6.702,6.759,6.817,6.873,6.931,6.989,7.047,7.105,7.164,7.222,7.281,7.34,7.399],
  [5.992,6.044,6.097,6.151,6.205,6.26,6.314,6.369,6.425,6.48,6.536,6.592,6.648,6.704,6.761,6.819,6.875,6.933,6.991,7.049,7.107,7.166,7.224,7.283,7.342,7.401],
  [5.995,6.047,6.1,6.154,6.208,6.262,6.317,6.372,6.427,6.482,6.538,6.594,6.65,6.707,6.764,6.821,6.878,6.936,6.993,7.051,7.109,7.168,7.226,7.285,7.344,7.403],
  [5.998,6.049,6.103,6.157,6.21,6.265,6.319,6.374,6.43,6.485,6.541,6.597,6.653,6.709,6.766,6.823,6.88,6.938,6.996,7.054,7.112,7.17,7.229,7.287,7.346,7.405],
  [6.001,6.053,6.106,6.16,6.214,6.268,6.322,6.377,6.433,6.488,6.544,6.599,6.656,6.712,6.769,6.826,6.883,6.941,6.999,7.056,7.114,7.173,7.231,7.29,7.349,7.408],
  [6.005,6.056,6.109,6.163,6.217,6.271,6.326,6.381,6.436,6.491,6.547,6.603,6.659,6.715,6.772,6.829,6.886,6.944,7.002,7.059,7.117,7.176,7.234,7.293,7.352,7.411],
  [6.009,6.06,6.113,6.167,6.221,6.275,6.329,6.384,6.439,6.495,6.55,6.606,6.662,6.719,6.775,6.832,6.889,6.947,7.005,7.062,7.12,7.179,7.237,7.296,7.355,7.414],
  [6.013,6.064,6.117,6.171,6.225,6.279,6.333,6.388,6.443,6.498,6.554,6.61,6.666,6.722,6.779,6.836,6.893,6.951,7.008,7.066,7.124,7.182,7.241,7.299,7.358,7.417],
  [6.017,6.068,6.121,6.175,6.229,6.283,6.337,6.392,6.447,6.502,6.558,6.614,6.67,6.726,6.783,6.84,6.897,6.954,7.012,7.07,7.128,7.186,7.244,7.303,7.362,7.421],
  [6.022,6.073,6.126,6.18,6.233,6.288,6.342,6.397,6.452,6.507,6.562,6.618,6.674,6.73,6.787,6.844,6.901,6.958,7.016,7.074,7.132,7.19,7.248,7.307,7.365,7.424],
  [6.027,6.078,6.131,6.184,6.238,6.292,6.347,6.401,6.456,6.512,6.567,6.623,6.679,6.735,6.792,6.848,6.905,6.963,7.02,7.078,7.136,7.194,7.252,7.311,7.369,7.429],
  [6.033,6.083,6.136,6.19,6.244,6.298,6.352,6.406,6.461,6.516,6.572,6.628,6.683,6.74,6.796,6.853,6.91,6.967,7.025,7.083,7.14,7.199,7.257,7.315,7.374,7.433],
  [6.039,6.089,6.142,6.195,6.249,6.303,6.357,6.412,6.467,6.522,6.577,6.633,6.689,6.745,6.801,6.858,6.915,6.972,7.03,7.087,7.145,7.203,7.261,7.32,7.378,7.437],
  [6.045,6.095,6.148,6.201,6.255,6.309,6.363,6.418,6.472,6.527,6.583,6.638,6.694,6.75,6.807,6.863,6.92,6.978,7.035,7.093,7.15,7.208,7.266,7.325,7.383,7.442],
  [6.052,6.101,6.154,6.208,6.261,6.315,6.369,6.424,6.478,6.533,6.589,6.644,6.7,6.756,6.812,6.869,6.926,6.983,7.04,7.098,7.156,7.214,7.272,7.33,7.388,7.447],
  [6.059,6.108,6.161,6.214,6.268,6.321,6.376,6.43,6.485,6.539,6.595,6.65,6.706,6.762,6.818,6.875,6.932,6.989,7.046,7.104,7.161,7.219,7.277,7.335,7.394,7.453],
  [6.066,6.115,6.168,6.221,6.274,6.328,6.382,6.437,6.491,6.546,6.601,6.657,6.712,6.768,6.824,6.881,6.938,6.995,7.052,7.11,7.167,7.225,7.283,7.341,7.4,7.458],
  [6.074,6.123,6.175,6.228,6.282,6.335,6.389,6.444,6.498,6.553,6.608,6.663,6.719,6.775,6.831,6.888,6.944,7.001,7.058,7.116,7.173,7.231,7.289,7.347,7.406,7.464],
  [6.082,6.131,6.183,6.236,6.289,6.343,6.397,6.451,6.505,6.56,6.615,6.67,6.726,6.782,6.838,6.894,6.951,7.008,7.065,7.122,7.18,7.237,7.295,7.353,7.412,7.47],
  [6.09,6.139,6.191,6.244,6.297,6.351,6.404,6.459,6.513,6.568,6.623,6.678,6.733,6.789,6.845,6.902,6.958,7.015,7.072,7.129,7.187,7.244,7.302,7.36,7.418,7.477],
  [6.099,6.148,6.199,6.252,6.305,6.359,6.413,6.467,6.521,6.576,6.63,6.686,6.741,6.797,6.853,6.909,6.966,7.022,7.079,7.136,7.194,7.251,7.309,7.367,7.425,7.483],
  [6.108,6.157,6.208,6.261,6.314,6.367,6.421,6.475,6.529,6.584,6.638,6.694,6.749,6.805,6.861,6.917,6.973,7.03,7.087,7.144,7.201,7.258,7.316,7.374,7.432,7.49],
  [6.118,6.166,6.218,6.27,6.323,6.376,6.43,6.484,6.538,6.592,6.647,6.702,6.757,6.813,6.869,6.925,6.981,7.038,7.095,7.151,7.209,7.266,7.324,7.381,7.439,7.498],
  [6.128,6.176,6.227,6.279,6.332,6.386,6.439,6.493,6.547,6.601,6.656,6.711,6.766,6.821,6.877,6.933,6.989,7.046,7.103,7.16,7.217,7.274,7.331,7.389,7.447,7.505],
  [6.139,6.187,6.237,6.289,6.342,6.395,6.449,6.502,6.556,6.61,6.665,6.72,6.775,6.83,6.886,6.942,6.998,7.055,7.111,7.168,7.225,7.282,7.34,7.397,7.455,7.513],
  [6.149,6.198,6.248,6.3,6.352,6.405,6.459,6.512,6.566,6.62,6.674,6.729,6.784,6.84,6.895,6.951,7.007,7.063,7.12,7.177,7.234,7.291,7.348,7.405,7.463,7.521],
  [6.161,6.209,6.259,6.31,6.363,6.416,6.469,6.522,6.576,6.63,6.684,6.739,6.794,6.849,6.905,6.96,7.016,7.073,7.129,7.186,7.242,7.299,7.357,7.414,7.472,7.53],
  [6.172,6.22,6.27,6.322,6.374,6.427,6.48,6.533,6.587,6.64,6.695,6.749,6.804,6.859,6.915,6.97,7.026,7.082,7.139,7.195,7.252,7.309,7.366,7.423,7.481,7.539],
  [6.184,6.232,6.282,6.333,6.385,6.438,6.491,6.544,6.597,6.651,6.705,6.76,6.815,6.87,6.925,6.98,7.036,7.092,7.148,7.205,7.261,7.318,7.375,7.432,7.49,7.548],
  [6.197,6.245,6.294,6.345,6.397,6.449,6.502,6.555,6.609,6.662,6.717,6.771,6.825,6.88,6.935,6.991,7.046,7.102,7.158,7.215,7.271,7.328,7.385,7.442,7.5,7.557],
  [6.21,6.258,6.307,6.358,6.409,6.461,6.514,6.567,6.62,6.674,6.728,6.782,6.837,6.891,6.946,7.002,7.057,7.113,7.169,7.225,7.281,7.338,7.395,7.452,7.509,7.567]
]

// Convert tableDataArr to tableData format
const tableData = arr2.map((flow, flowIndex) => {
  const row: any = { flow: flow.toString() };
  arr.forEach((waterLevel, levelIndex) => {
    row[waterLevel.toString()] = tableDataArr[flowIndex][levelIndex].toFixed(3);
  });
  return row;
});

import { useThemeStore } from '@/stores/theme';
// import DongKuRong2 from "@/views/screen/components/right/DongKuRong2.vue";
const themeStore = useThemeStore()
themeStore.$subscribe(() => {
  chartInstance1.dispose() //销毁当前图表
  initChart1() //用新的主题初始化图表对象
  initChart2();
  screenAdapter() //更新适配
  updateChart1()  //更新展示
  updateChart2();
})
onMounted(() => {
  initChart1()
  initChart2();
  getData() //获取数据
  window.addEventListener('resize', screenAdapter)
  // 页面加载完成主动进行屏幕的适配
  screenAdapter()
})
onUnmounted(() => {
  //避免出现内存泄露
  window.removeEventListener('resize', screenAdapter)
  chartInstance1?.dispose();
  chartInstance2?.dispose();
  // 组件销毁时 进行回调函数的取消
  // proxy.$socket.unRegisterCallBack("trendData")
})
//初始化echartInstance对象
function initChart1() {
  // 初始化图表
  chartInstance1 = proxy.$echarts.init(trend_ref1.value, 'dark-blue')
  // 对图表初始化配置的控制,删除数据以及图的大小（这是分辨率适配）
  const initOption = {}
  chartInstance1.setOption(initOption)
}
// 初始化第二个图表
function initChart2() {
  // 初始化图表
  chartInstance2 = proxy.$echarts.init(trend_ref2.value, 'dark-blue');
  const initOption = {};
  chartInstance2.setOption(initOption);
}

//获取服务器的数据
async function getData() {
  // 直接使用tableData
  updateChart1()
  updateChart2();
}

//更新图表
function updateChart1() {
  // Select every 5th water level for display to avoid overcrowding
  const displayWaterLevels = arr.filter((_, index) => index % 5 === 0);

  // 自定义颜色数组，确保颜色差异明显
  const customColors = [
    '#00c6ff', // 亮蓝色
    '#ff9500', // 橙色
    '#00ff95', // 绿色
    '#ff00c8', // 粉色
    '#ffff00', // 黄色
    '#9d00ff'  // 紫色
  ];

  const dataOption = {
    title: {
      text: '三-葛区间动库容(10^8 m³)',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#00ffff'
      },
      top: '20px'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: displayWaterLevels.map(level => level.toString()),
      textStyle: {
        fontSize: 14,
        color: '#ffffff'
      }
      // type: 'scroll',
      // bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: {
        fontSize: 14
      },
      data: arr2.map(flow => flow.toString())
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 14
      },
      min: 5.5,
      max: 8
    },
    color: customColors,
    series: displayWaterLevels.map(level => ({
      name: level.toString(),
      type: 'line',
      smooth: true,
      data: arr2.map((_, flowIndex) => tableDataArr[flowIndex][arr.indexOf(level)])
    }))
  }

  chartInstance1.setOption(dataOption)
}

//更新图表
function updateChart2() {
  // Select a subset of flows for better visualization
  const selectedFlows = [ 10000, 20000, 30000, 40000, 50000, 60000];

  // 自定义颜色数组，确保颜色差异明显
  const customColors = [
    '#00c6ff', // 亮蓝色
    '#ff9500', // 橙色
    '#00ff95', // 绿色
    '#ff00c8', // 粉色
    '#ffff00', // 黄色
    '#9d00ff'  // 紫色
  ];

  const series = selectedFlows.map(flow => {
    const flowIndex = arr2.indexOf(flow);
    return {
      name: flow.toString(),
      type: 'line',
      smooth: true,
      // symbol: 'circle',
      symbolSize: 6,
      lineStyle: { width: 2 },
      data: arr.map((_, levelIndex) => tableDataArr[flowIndex][levelIndex])
    };
  });

  const dataOption = {
    title: {
      text: '三-葛区间动库容(10^8 m³)',
      textStyle: { fontSize: 16, fontWeight: 'bold', color: '#00ffff'},
      // left: 'center',
      top: '20px'
    },
    tooltip: {
      trigger: 'axis',
      // axisPointer: { type: 'cross' }
    },
    legend: {
      data: selectedFlows.map(f => f.toString()),
      textStyle: {
        fontSize: 14,
        color: '#ffffff'
      }
      // type: 'scroll',
      // bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: {
        fontSize: 14
      },
      data: arr.map(l => l.toString())
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 14
      },
      min: 5.5,
      max: 8
    },
    color: customColors,
    series,
    // color: ['#c23531','#2f4554','#61a0a8','#d48265','#91c7ae','#749f83','#ca8622','#bda29a']
  };
  chartInstance2.setOption(dataOption);
}

//当浏览器大小发生变化的时候，会调用的方法，完成屏幕的适配
function screenAdapter() {
  chartInstance1?.resize();
  chartInstance2?.resize();
}

const num1 = ref(62)
const num2 = ref(4000)
const result = ref(5.983)

function bilinearInterpolation(x:any, y:any, data:any) {
  // Find the indices for x (water level)
  let xIndex1 = Math.floor((num1.value - 62)/0.2)
  let xIndex2 = Math.min(xIndex1 + 1, arr.length - 1)

  // Find the indices for y (flow)
  let yIndex1 = Math.floor((num2.value - 4000)/2000)
  let yIndex2 = Math.min(yIndex1 + 1, arr2.length - 1)

  // Get the surrounding values
  let x1 = arr[xIndex1]
  let x2 = arr[xIndex2]
  let y1 = arr2[yIndex1]
  let y2 = arr2[yIndex2]

  // Perform bilinear interpolation
  let Q11 = data[yIndex1][xIndex1]
  let Q12 = data[yIndex1][xIndex2]
  let Q21 = data[yIndex2][xIndex1]
  let Q22 = data[yIndex2][xIndex2]

  let R1 = ((x2 - x)/(x2 - x1)) * Q11 + ((x - x1)/(x2 - x1)) * Q21
  let R2 = ((x2 - x)/(x2 - x1)) * Q12 + ((x - x1)/(x2 - x1)) * Q22

  let Z = ((y2 - y)/(y2 - y1)) * R1 + ((y - y1)/(y2 - y1)) * R2

  return Z;
}

function queryData() {
  result.value = bilinearInterpolation(num1.value, num2.value, tableDataArr)
}

function formatNoDecimal(value:any) {
  return value.toFixed(3);
}

defineExpose({
  onMounted,
  updateChart1,
  updateChart2,
  screenAdapter
});
</script>

<template>
  <CommonLayout>
    <div class="container">
      <div class="page-title">三-葛区间动库容计算</div>
      <div class="table-container">
        <div class="table-panel">
          <div class="panel-title">动库容数据表</div>
          <div class="table-wrapper">
            <el-table
              :data="tableData"
              style="max-height: 280px; overflow-y: auto;"
              class="custom-table"
              :header-cell-style="{backgroundColor: 'rgba(13, 34, 82, 0.8)', color: '#00ffff', fontWeight: 'bold', fontSize: '15px'}"
              :row-style="{backgroundColor: 'rgba(10, 26, 58, 0.6)'}"
              :cell-style="{color: '#ffffff', fontSize: '14px'}"
              :bg-color="'transparent'"
              table-layout="fixed"
              size="small"
            >
            <el-table-column prop="flow" label="流量(m³/s)" width="120" align="center" fixed/>
            <el-table-column label="水位(m)" align="center">
              <el-table-column
                v-for="level in arr.filter((_, index) => index % 3 === 0)"
                :key="level"
                :prop="level.toString()"
                :label="level.toString()"
                width="100"
                align="center"
              />
            </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="query-panel">
          <div class="panel-title">动库容查询</div>
          <div class="query-form">
            <div class="input-row">
              <div class="form-item">
                <el-text class="label">水位(m)：</el-text>
                <el-input-number
                  v-model="num1"
                  :precision="1"
                  :step="0.2"
                  :min="62"
                  :max="67"
                  class="custom-input"
                  size="small"
                />
              </div>
              <div class="form-item">
                <el-text class="label">流量(m³/s)：</el-text>
                <el-input-number
                  v-model="num2"
                  :precision="0"
                  :step="2000"
                  :min="4000"
                  :max="74000"
                  class="custom-input"
                  size="small"
                />
              </div>
            </div>
            <el-button type="primary" @click="queryData" class="query-btn">查询</el-button>

            <div class="result-container">
              <div class="result-label">计算结果 (10^8 m³)：</div>
              <div class="result-value">
                <el-statistic :value="result" :formatter="formatNoDecimal" class="custom-statistic">
                </el-statistic>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="chart-container">
        <div class="chart-panel">
          <div class="com-container">
            <div class="com-chart1" ref="trend_ref2"></div>
          </div>
        </div>
        <div class="chart-panel">
          <div class="com-container">
            <div class="com-chart1" ref="trend_ref1"></div>
          </div>
        </div>
      </div>
    </div>
  </CommonLayout>
</template>

<style lang="scss" scoped>
// Main colors
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$data-highlight: #00ffff;

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: calc(100vh - 120px); /* 减去头部和导航的高度 */
  // padding: 5px;
  color: $text-light;
  overflow-y: auto;
}

.page-title {
  font-size: 26px;
  font-weight: bold;
  color: $text-highlight;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
  margin-bottom: 20px;
  text-align: center;
  width: 100%;
}

.table-container {
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.table-panel, .query-panel, .chart-panel {
  background-color: rgba($secondary-dark-blue, 0.7);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 5px;
  box-shadow: 0 0 20px rgba($glow-blue, 0.2);
  padding: 15px;
}

.table-panel {
  width: 65%;
  height: 380px;
  display: flex;
  flex-direction: column;
}

.table-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  overflow: hidden;
  padding: 0 10px;
}

.query-panel {
  width: 35%;
  height: 380px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-title {
  color: $text-highlight;
  font-size: 18px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba($border-glow, 0.3);
  padding-bottom: 8px;
  text-align: center;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
}

.custom-table {
  --el-table-border-color: rgba(0, 136, 255, 0.3);
  --el-table-border: 1px solid var(--el-table-border-color);
  --el-table-text-color: #ffffff;
  --el-table-header-text-color: #00ffff;
  --el-table-row-hover-bg-color: rgba(0, 168, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  width: auto;
  margin: 0 auto;
  font-size: 14px;
}

.query-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

.input-row {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  gap: 10px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 48%;
}

.label {
  color: $text-light;
  font-size: 15px;
}

.custom-input {
  width: 100%;
  margin-top: 10px;
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-text-color: #ffffff;
  --el-input-border-color: rgba(0, 136, 255, 0.5);
  --el-input-hover-border-color: rgba(0, 168, 255, 0.8);
}

.query-btn {
  margin-top: 15px;
  background-color: $highlight-blue;
  border-color: $highlight-blue;
  color: $text-light;
  font-weight: bold;
  width: 100%;
  font-size: 15px;
  height: 36px;
  &:hover {
    background-color: darken($highlight-blue, 10%);
    border-color: darken($highlight-blue, 10%);
  }
}

.result-container {
  margin-top: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba($border-glow, 0.5);
  border-radius: 5px;
  padding: 8px;
}

.result-label {
  font-size: 15px;
  color: $text-highlight;
  margin-bottom: 3px;
  text-align: center;
}

.result-value {
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-statistic {
  --el-statistic-content-font-size: 32px;
  --el-statistic-content-color: $data-highlight;
}

.chart-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 20px;
  margin-bottom: 20px; /* 添加底部边距，防止内容超出边界 */
}

.chart-panel {
  width: 48%;
  height: 280px; /* 减小高度，防止超出边界 */
}

.com-container {
  width: 100%;
  height: 100%;
}

.com-chart1 {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// 适配深色模式下的Element Plus组件
:deep(.el-input-number) {
  --el-input-bg-color: rgba(10, 26, 58, 0.6);
  --el-input-text-color: #ffffff;
  --el-input-border-color: rgba(0, 136, 255, 0.5);
  --el-input-hover-border-color: rgba(0, 168, 255, 0.8);
  width: 100%;
  height: 32px;
  font-size: 14px;
}

:deep(.el-input__wrapper) {
  background-color: rgba(10, 26, 58, 0.6);
  box-shadow: 0 0 0 1px rgba(0, 136, 255, 0.5) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px rgba(0, 198, 255, 0.8) inset;
}

:deep(.el-input__inner) {
  color: #ffffff;
  background-color: transparent;
  font-size: 14px;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background-color: rgba(13, 34, 82, 0.8);
  color: #00ffff;
  border-color: rgba(0, 136, 255, 0.5);
}

:deep(.el-button) {
  background-color: $highlight-blue;
  border-color: $highlight-blue;
  &:hover {
    background-color: darken($highlight-blue, 10%);
    border-color: darken($highlight-blue, 10%);
  }
}

/* 修复表格右侧白色区域 */
:deep(.el-table) {
  background-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-bg-color: transparent;
  --el-table-border-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__inner-wrapper::before) {
  display: none;
}

:deep(.el-table__fixed-right) {
  background-color: rgba(10, 26, 58, 0.6);
  height: 100% !important;
}

:deep(.el-table__fixed-right-patch) {
  background-color: rgba(13, 34, 82, 0.8);
}

:deep(.el-table__cell) {
  background-color: transparent;
}

:deep(.el-table--border::after),
:deep(.el-table--border::before),
:deep(.el-table__inner-wrapper::before) {
  background-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__fixed::before),
:deep(.el-table__fixed-right::before) {
  background-color: transparent;
}
</style>