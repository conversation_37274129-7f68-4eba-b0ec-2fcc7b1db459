import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/login/index.vue'),
      meta: { requiresAuth: false }
    },
    {
      path:'/home',
      component:() => import('@/views/screen/index.vue'),
      meta: { requiresAuth: true }
    },
    {
      path:'/1',
      component:() => import('@/views/screen/components/right/DongKuRong.vue'),
      meta: { requiresAuth: true }
    },
    {
      path:'/2',
      component:() => import('@/views/screen/components/right/San-Time.vue'),
      meta: { requiresAuth: true }
    },
    {
      path:'/3',
      component:() => import('@/views/screen/components/right/XiaoXiTa-Time.vue'),
      meta: { requiresAuth: true }
    },
    {
      path:'/4',
      component:() => import('@/views/screen/components/right/SangHui.vue'),
      meta: { requiresAuth: true }
    },
    {
      path:'/5',
      component:() => import('@/views/screen/components/right/GeHui.vue'),
      meta: { requiresAuth: true }
    },
    // {
    //   path:'/6',
    //   component:() => import('@/views/screen/components/Data.vue'),
    //   meta: { requiresAuth: true }
    // },
    {
      path:'/7',
      component:() => import('@/views/screen/components/scheduling.vue'),
      meta: { requiresAuth: true }
    },
    {
      path:'/stks',
      component:() => import('@/views/screen/components/STKS.vue'),
      meta: { requiresAuth: true }
    },
    {
      path:'/map',
      component:() => import('@/views/screen/components/SanGeMap.vue'),
      meta: { requiresAuth: true }
    },
  ]
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  // 从localStorage获取用户信息
  const userInfoStr = localStorage.getItem('userInfo')
  const isLoggedIn = userInfoStr ? JSON.parse(userInfoStr).isLoggedIn : false

  if (requiresAuth && !isLoggedIn) {
    // 需要认证但未登录，重定向到登录页
    next({ path: '/login' })
  } else if (to.path === '/login' && isLoggedIn) {
    // 已登录用户访问登录页，重定向到首页
    next({ path: '/home' })
  } else {
    // 其他情况正常导航
    next()
  }
})

export default router
