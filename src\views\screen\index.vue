<script setup lang="ts" name="ScreenPage">
import {ref, onMounted} from "vue";
import Picture from '@/views/screen/components/Picture.vue';
import { useUserStore } from '@/stores/userStore';

// 用户Store
const userStore = useUserStore();

// 容器样式
const containerStyle = ref('')

// Navigation tabs
const navTabs = ref([
  { id: 'home', label: '首页', active: true },
  { id: '1', label: '动库容计算', active: false },
  { id: '2', label: '三-葛传播时间', active: false },
  { id: '3', label: '小溪塔传播时间', active: false },
  { id: '4', label: '三-葛回水顶托', active: false },
  { id: '5', label: '葛洲坝回水顶托', active: false },
  { id: '6', label: '水文水动力模型计算', active: false },
  { id: '7', label: '优化调度计算', active: false },
])

// Data counters
const dataCounters = ref({
  totalDevices: 38,
  totalPower: 113, // 单位：千瓦时
  totalFlow: 27,     // 单位：立方米/秒
  totalOutput: 15.8,   // 单位：兆瓦
  upstreamWaterLevel: 58855, // 单位：厘米
  downstreamWaterLevel: 3745, // 单位：厘米
  reservoirStorage: 1600,    // 单位：百万立方米
  waterDischarge: 1969       // 单位：立方米/秒
})

// 导入路由
import { useRouter } from 'vue-router';
import SanGeMap from "./components/SanGeMap.vue";
const router = useRouter();

// 处理登出
const handleLogout = () => {
  // 清除用户信息
  userStore.clearUserInfo();
  // 跳转到登录页
  router.push('/login');
};

// Set active tab and navigate to the corresponding route
const setActiveTab = (tabId: string) => {
  navTabs.value = navTabs.value.map(tab => ({
    ...tab,
    active: tab.id === tabId
  }))

  // 处理路由跳转
  if (tabId === 'home') {
    router.push('/home');
  } else if (['1', '2', '3', '4', '5', '6', '7'].includes(tabId)) {
    router.push(`/${tabId}`);
  }
}

onMounted(() => {
  // Any initialization code here
})

</script>

<template>
  <div class="screen-container" :style="containerStyle">
    <header class="screen-header">
      <div class="header-content">
        <span class="logo">
          <img src="/static/img/长江电力.png" alt="Logo" />
        </span>
        <span class="title">实时动库容对三峡-葛洲坝梯级短期调度影响</span>
        <div class="title-right">
          <span class="system-version">系统版本</span>
          <span class="login-info" @click="handleLogout">退出登录</span>
          <span class="about">关于</span>
        </div>
      </div>
    </header>

    <!-- Navigation Tabs -->
    <div class="nav-tabs-container">
      <div class="nav-tabs">
        <div
          v-for="tab in navTabs"
          :key="tab.id"
          class="nav-tab"
          :class="{ active: tab.active }"
          @click="setActiveTab(tab.id)"
        >
          {{ tab.label }}
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="screen-body">
      <!-- Data Counters -->
      <div class="data-counters">
        <div class="counter-item">
          <div class="counter-label">三峡-葛洲坝河道距离(公里)</div>
          <div class="digital-counter">{{ dataCounters.totalDevices }}</div>
        </div>
        <div class="counter-item">
          <div class="counter-label">三峡水位落差(米)</div>
          <div class="digital-counter">{{ dataCounters.totalPower }}</div>
        </div>
        <div class="counter-item">
          <div class="counter-label">葛洲坝水位落差(米)</div>
          <div class="digital-counter">{{ dataCounters.totalFlow }}</div>
        </div>
        <div class="counter-item">
          <div class="counter-label">葛洲坝总库容(亿立方米)</div>
          <div class="digital-counter">{{ dataCounters.totalOutput }}</div>
        </div>
      </div>

      <!-- Map Section -->
      <div class="map-container glow-border">
        <!-- <Picture /> -->
        <SanGeMap />

        <!-- Data Panels -->
        <!-- <div class="data-panels">
          <div class="data-panel">
            <div class="panel-title">流域上游水位(厘米)</div>
            <div class="data-value">{{ dataCounters.upstreamWaterLevel }}</div>
          </div>
          <div class="data-panel">
            <div class="panel-title">区域调上水位(厘米)</div>
            <div class="data-value">{{ dataCounters.downstreamWaterLevel }}</div>
          </div>
          <div class="data-panel">
            <div class="panel-title">大坝蓄水量(百万立方米)</div>
            <div class="data-value">{{ dataCounters.reservoirStorage }}</div>
          </div>
          <div class="data-panel">
            <div class="panel-title">可用排水量(立方米/秒)</div>
            <div class="data-value">{{ dataCounters.waterDischarge }}</div>
          </div>
        </div> -->
      </div>

      <!-- Side Menu -->
      <div class="side-menu">
        <div class="menu-item" v-for="(item, index) in ['关于我们', '在线咨询', '综合效率', '调控文化', '快速帮助', '视频帮助']" :key="index">
          {{ item }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// Main colors
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$map-highlight: #00a8ff;
$data-highlight: #00ffff;
$nav-active: #00a8ff;
$nav-inactive: #1e3c72;

// Global container
.screen-container {
  width: 100%;
  height: 100%;
  padding: 0;
  background-color: $primary-dark-blue;
  background-image: linear-gradient(to bottom, $secondary-dark-blue, $primary-dark-blue);
  color: $text-light;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('./static/img/grid-pattern.png');
    background-size: 20px 20px;
    opacity: 0.1;
    pointer-events: none;
    z-index: 1;
  }
}

// Header styles
.screen-header {
  width: 100%;
  height: 60px;
  font-size: 20px;
  position: relative;
  z-index: 10;
  border-bottom: 1px solid rgba($border-glow, 0.3);

  .header-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 20px;
    width: 100%;
    position: relative; /* 添加相对定位 */
  }

  .title {
    color: $text-light;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba($glow-blue, 0.5);
    letter-spacing: 2px;
    text-align: center;
    position: absolute; /* 使用绝对定位 */
    left: 50%; /* 左边缘在容器中心 */
    top: 50%; /* 上边缘在容器中心 */
    transform: translate(-50%, -50%); /* 向左上偏移自身宽高的一半，实现真正居中 */
    white-space: nowrap; /* 防止标题换行 */
    z-index: 1; /* 确保标题在其他元素之上 */
  }

  .title-right {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-left: auto; /* 将右侧元素推到最右边 */
    z-index: 2; /* 确保右侧元素在标题之上 */

    span {
      font-size: 14px;
      cursor: pointer;
      padding: 5px 10px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba($highlight-blue, 0.2);
      }
    }
  }

  .logo {
    display: flex;
    align-items: center;
    z-index: 2; /* 确保logo在标题之上 */

    img {
      height: 40px;
      width: auto;
    }
  }
}

// Navigation tabs
.nav-tabs-container {
  padding: 0 20px;
  margin-top: 5px;
  z-index: 10;
  position: relative;
  display: flex;
  justify-content: center; // 整体居中显示
}

.nav-tabs {
  display: flex;
  border-bottom: 1px solid rgba($border-glow, 0.2);
  justify-content: center; // 内容居中

  .nav-tab {
    padding: 8px 20px;
    margin-right: 2px;
    background-color: $nav-inactive;
    color: $text-light;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px; // 设置固定宽度
    text-align: center; // 文字居中

    &.active {
      background-color: $nav-active;
      color: $text-highlight;
      box-shadow: 0 0 15px rgba($glow-blue, 0.5);
    }

    &:hover:not(.active) {
      background-color: lighten($nav-inactive, 10%);
    }
  }
}

// Main content area
.screen-body {
  width: 100%;
  height: calc(100% - 110px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
}

// Data counters
.data-counters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .counter-item {
    background-color: rgba($secondary-dark-blue, 0.7);
    border: 1px solid rgba($border-glow, 0.3);
    border-radius: 5px;
    padding: 10px 15px;
    flex: 1;
    margin: 0 5px;
    text-align: center;
    box-shadow: 0 0 20px rgba($glow-blue, 0.2);

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  .counter-label {
    font-size: 14px;
    color: rgba($text-light, 0.8);
    margin-bottom: 5px;
  }

  .digital-counter {
    font-family: 'Digital Font', 'Consolas', 'Courier New', monospace;
    font-size: 28px;
    color: $data-highlight;
    text-shadow: 0 0 10px rgba($data-highlight, 0.7);
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba($border-glow, 0.5);
    border-radius: 3px;
    padding: 5px;
    letter-spacing: 2px;
  }
}

// Map container
.map-container {
  flex: 1;
  position: relative;
  border: 1px solid rgba($border-glow, 0.5);
  box-shadow: 0 0 15px rgba($glow-blue, 0.3);
  border-radius: 5px;
  overflow: hidden;
  background-color: rgba($secondary-dark-blue, 0.3);
  display: flex;
  flex-direction: column;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    background: radial-gradient(circle at center, transparent 30%, rgba($primary-dark-blue, 0.5) 100%);
    z-index: 2;
  }
}

// Data panels
.data-panels {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  background-color: rgba($primary-dark-blue, 0.7);
  border-top: 1px solid rgba($border-glow, 0.3);
  z-index: 3;
  position: relative;

  .data-panel {
    flex: 1;
    margin: 0 10px;
    text-align: center;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  .panel-title {
    color: $text-highlight;
    font-size: 14px;
    margin-bottom: 5px;
  }

  .data-value {
    font-family: 'Digital Font', 'Consolas', 'Courier New', monospace;
    font-size: 24px;
    color: $data-highlight;
    text-shadow: 0 0 10px rgba($data-highlight, 0.7);
  }
}

// Side menu
.side-menu {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 20;

  .menu-item {
    background-color: rgba($secondary-dark-blue, 0.8);
    border: 1px solid rgba($border-glow, 0.5);
    border-radius: 5px;
    padding: 10px 15px;
    color: $text-light;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba($glow-blue, 0.2);

    &:hover {
      background-color: rgba($highlight-blue, 0.3);
      box-shadow: 0 0 15px rgba($glow-blue, 0.5);
    }
  }
}

// Responsive adjustments
@media (max-width: 1600px) {
  .data-counters .digital-counter {
    font-size: 24px;
  }

  .data-panels .data-value {
    font-size: 20px;
  }

  .side-menu .menu-item {
    padding: 8px 12px;
    font-size: 14px;
  }
}
</style>
