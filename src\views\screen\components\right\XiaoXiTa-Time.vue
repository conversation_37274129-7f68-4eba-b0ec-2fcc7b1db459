<script setup lang="ts">
import { getCurrentInstance, ref, onMounted, onUnmounted } from "vue";
import CommonLayout from '@/components/CommonLayout.vue';
const { proxy } = getCurrentInstance() as any;

// 获取html元素
const trend_ref1 = ref();
const trend_ref2 = ref();
let chartInstance1: any = null;
let chartInstance2: any = null;
// 移除未使用的变量

const flowArr = [300, 500, 700, 900, 1100, 1300, 1500, 1700, 1900, 2100];
const waterLevelArr = [62.5, 63, 63.5, 64, 64.5, 65, 65.5, 66, 66.5, 67];
// 原始数据（以水位为行，流量为列）
const originalDataArr = [
  [121, 115, 110, 105, 101, 98, 94, 91, 90, 88],
  [87, 84, 80, 76, 72, 71, 69, 68, 67, 66],
  [64, 61, 58, 57, 55, 54, 52, 51, 49, 43],
  [53, 50, 47, 44, 42, 42, 40, 38, 37, 36],
  [43, 42, 41, 38, 36, 36, 35, 34, 33, 32],
  [38, 36, 35, 34, 33, 32, 31, 30, 30, 29],
  [35, 33, 32, 31, 30, 29, 29, 28, 28, 27],
  [32, 31, 30, 29, 28, 27, 27, 26, 26, 25],
  [30, 29, 29, 28, 27, 27, 26, 25, 25, 24],
  [29, 28, 27, 27, 26, 26, 25, 25, 25, 24]
];

// 转置数据（以流量为行，水位为列）
const tableDataArr = flowArr.map((_, flowIndex) => {
  return waterLevelArr.map((_, levelIndex) => {
    return originalDataArr[levelIndex][flowIndex];
  });
});

// 调整表格数据结构，以流量为行，水位为列，与DongKuRong.vue保持一致
const tableData = flowArr.map((flow, flowIndex) => {
  const row: any = { flow: flow.toString() };
  waterLevelArr.forEach((level, levelIndex) => {
    row[level.toString()] = tableDataArr[flowIndex][levelIndex].toString();
  });
  return row;
});

import { useThemeStore } from '@/stores/theme';
const themeStore = useThemeStore();

themeStore.$subscribe(() => {
  chartInstance1?.dispose();
  chartInstance2?.dispose();
  initChart1();
  initChart2();
  screenAdapter();
  updateChart1();
  updateChart2();
});

onMounted(() => {
  initChart1();
  initChart2();
  getData();
  window.addEventListener('resize', screenAdapter);
  screenAdapter();
});

onUnmounted(() => {
  window.removeEventListener('resize', screenAdapter);
  chartInstance1?.dispose();
  chartInstance2?.dispose();
});

// 初始化第一个图表
function initChart1() {
  // 初始化图表
  chartInstance1 = proxy.$echarts.init(trend_ref1.value, 'dark-blue');
  const initOption = {};
  chartInstance1.setOption(initOption);
}

// 初始化第二个图表
function initChart2() {
  // 初始化图表
  chartInstance2 = proxy.$echarts.init(trend_ref2.value, 'dark-blue');
  const initOption = {};
  chartInstance2.setOption(initOption);
}

// 获取数据
async function getData() {
  // 更新图表数据
  updateChart1();
  updateChart2();
}

// 定义间隔步长，每隔2个数据显示一个
const displayStep = 2;

// 修改 updateChart1 函数
function updateChart1() {
  // 筛选要显示的流量数据（每隔displayStep显示一个）
  const displayedFlows = flowArr.filter((_, index) => index % displayStep === 0);

  const dataOption = {
    title: {
      text: '传播时间(min)',
      textStyle: { fontSize: 16, fontWeight: 'bold', color: '#00ffff' },
      top: '20px'
    },
    tooltip: { trigger: 'axis' },
    legend: {
      data: displayedFlows.map(f => f.toString()),
      textStyle: {
        fontSize: 14,
        color: '#ffffff'
      }
    },
    grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: { fontSize: 14 },
      data: waterLevelArr.map(w => w.toString())
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 14 },
      min: 20,
      max: 130
    },
    // 自定义颜色数组，确保颜色差异明显
    color: [
      '#00c6ff', // 亮蓝色
      '#ff9500', // 橙色
      '#00ff95', // 绿色
      '#ff00c8', // 粉色
      '#ffff00', // 黄色
      '#9d00ff'  // 紫色
    ],
    series: displayedFlows.map(flow => ({
      name: flow.toString(),
      type: 'line',
      smooth: true,
      data: waterLevelArr.map(level => {
        // 找到对应流量的行索引
        const flowIndex = flowArr.indexOf(flow);
        // 找到对应水位的列索引
        const levelIndex = waterLevelArr.indexOf(level);
        // 返回该点的数据
        return tableDataArr[flowIndex][levelIndex];
      })
    }))
  };
  chartInstance1.setOption(dataOption);
}
// 修改 updateChart2 函数
function updateChart2() {
  // 筛选要显示的水位数据（每隔displayStep显示一个）
  const displayedLevels = waterLevelArr.filter((_, index) => index % displayStep === 0);

  const dataOption = {
    title: {
      text: '传播时间(min)',
      textStyle: { fontSize: 16, fontWeight: 'bold', color: '#00ffff' },
      top: '20px'
    },
    tooltip: { trigger: 'axis' },
    legend: {
      data: displayedLevels.map(w => w.toString()),
      textStyle: {
        fontSize: 14,
        color: '#ffffff'
      }
    },
    grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: { fontSize: 14 },
      data: flowArr.map(f => f.toString())
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 14 },
      min: 20,
      max: 130
    },
    // 自定义颜色数组，确保颜色差异明显
    color: [
      '#00c6ff', // 亮蓝色
      '#ff9500', // 橙色
      '#00ff95', // 绿色
      '#ff00c8', // 粉色
      '#ffff00', // 黄色
      '#9d00ff'  // 紫色
    ],
    series: displayedLevels.map(level => ({
      name: level.toString(),
      type: 'line',
      smooth: true,
      data: flowArr.map((_, flowIndex) => tableDataArr[flowIndex][waterLevelArr.indexOf(level)])
    }))
  };
  chartInstance2.setOption(dataOption);
}

// 屏幕适配
function screenAdapter() {
  chartInstance1?.resize();
  chartInstance2?.resize();
}

// 计算相关
const num1 = ref(62.5);
const num2 = ref(300);
const result = ref(121);

function bilinearInterpolation(x: number, y: number, _: number[][]) {
  // 测试直接使用原始数据进行计算
  return bilinearInterpolationOriginal(x, y, originalDataArr);
}

// 使用原始数据进行双线性插值计算
function bilinearInterpolationOriginal(x: number, y: number, data: number[][]) {
  // 找到最近的四个点
  let x1 = 0, x2 = 0, y1 = 0, y2 = 0;
  let levelIndex = 0, flowIndex = 0;
  let found = false;

  // 找到水位(x)的区间
  for (let i = 0; i < waterLevelArr.length - 1; i++) {
    if (x >= waterLevelArr[i] && x <= waterLevelArr[i + 1]) {
      x1 = waterLevelArr[i];
      x2 = waterLevelArr[i + 1];
      levelIndex = i;
      found = true;
      break;
    }
  }

  // 如果没有找到匹配的区间，使用最近的区间
  if (!found) {
    if (x < waterLevelArr[0]) {
      x1 = waterLevelArr[0];
      x2 = waterLevelArr[1];
      levelIndex = 0;
    } else {
      const lastIndex = waterLevelArr.length - 1;
      x1 = waterLevelArr[lastIndex - 1];
      x2 = waterLevelArr[lastIndex];
      levelIndex = lastIndex - 1;
    }
  }

  // 重置标志
  found = false;

  // 找到流量(y)的区间
  for (let j = 0; j < flowArr.length - 1; j++) {
    if (y >= flowArr[j] && y <= flowArr[j + 1]) {
      y1 = flowArr[j];
      y2 = flowArr[j + 1];
      flowIndex = j;
      found = true;
      break;
    }
  }

  // 如果没有找到匹配的区间，使用最近的区间
  if (!found) {
    if (y < flowArr[0]) {
      y1 = flowArr[0];
      y2 = flowArr[1];
      flowIndex = 0;
    } else {
      const lastIndex = flowArr.length - 1;
      y1 = flowArr[lastIndex - 1];
      y2 = flowArr[lastIndex];
      flowIndex = lastIndex - 1;
    }
  }

  // 四个角的值 - 原始数据是[levelIndex][flowIndex]
  const Q11 = data[levelIndex][flowIndex];
  const Q12 = data[levelIndex][flowIndex + 1];
  const Q21 = data[levelIndex + 1][flowIndex];
  const Q22 = data[levelIndex + 1][flowIndex + 1];

  // 双线性插值计算
  const R1 = ((x2 - x) / (x2 - x1)) * Q11 + ((x - x1) / (x2 - x1)) * Q21;
  const R2 = ((x2 - x) / (x2 - x1)) * Q12 + ((x - x1) / (x2 - x1)) * Q22;
  const Z = ((y2 - y) / (y2 - y1)) * R1 + ((y - y1) / (y2 - y1)) * R2;

  return Z;
}

function queryData() {
  result.value = bilinearInterpolation(num1.value, num2.value, tableDataArr);
}

function formatNoDecimal(value: any) {
  return value.toFixed(2);
}

defineExpose({
  onMounted,
  updateChart1,
  updateChart2,
  screenAdapter
});
</script>

<template>
  <CommonLayout>
    <div class="container">
      <div class="page-title">小溪塔传播时间计算</div>
      <div class="table-container">
        <div class="table-panel">
          <div class="panel-title">传播时间数据表</div>
          <div class="table-wrapper">
            <el-table
              :data="tableData"
              style="max-height: 280px; overflow-y: auto;"
              class="custom-table"
              :header-cell-style="{backgroundColor: 'rgba(13, 34, 82, 0.8)', color: '#00ffff', fontWeight: 'bold', fontSize: '15px'}"
              :row-style="{backgroundColor: 'rgba(10, 26, 58, 0.6)'}"
              :cell-style="{color: '#ffffff', fontSize: '14px'}"
              :bg-color="'transparent'"
              table-layout="fixed"
              size="small"
            >
              <el-table-column prop="flow" label="流量(m³/s)" width="120" align="center" fixed/>
              <el-table-column label="水位(m)" align="center">
                <el-table-column
                  v-for="level in waterLevelArr"
                  :key="level"
                  :prop="level.toString()"
                  :label="level.toString()"
                  width="100"
                  align="center"
                />
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="query-panel">
          <div class="panel-title">传播时间查询</div>
          <div class="query-form">
            <div class="input-row">
              <div class="form-item">
                <el-text class="label">水位(m)：</el-text>
                <el-input-number
                  v-model="num1"
                  :precision="1"
                  :step="0.5"
                  :min="62.5"
                  :max="67"
                  class="custom-input"
                  size="small"
                />
              </div>
              <div class="form-item">
                <el-text class="label">流量(m³/s)：</el-text>
                <el-input-number
                  v-model="num2"
                  :precision="0"
                  :step="200"
                  :min="300"
                  :max="2100"
                  class="custom-input"
                  size="small"
                />
              </div>
            </div>
            <el-button type="primary" @click="queryData" class="query-btn">查询</el-button>

            <div class="result-container">
              <div class="result-label">计算结果 (min)：</div>
              <div class="result-value">
                <el-statistic :value="result" :formatter="formatNoDecimal" class="custom-statistic">
                </el-statistic>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="chart-container">
        <div class="chart-panel">
          <div class="com-container">
            <div class="com-chart1" ref="trend_ref1"></div>
          </div>
        </div>
        <div class="chart-panel">
          <div class="com-container">
            <div class="com-chart1" ref="trend_ref2"></div>
          </div>
        </div>
      </div>
    </div>
  </CommonLayout>
</template>

<style lang="scss" scoped>
// Main colors
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$data-highlight: #00ffff;

/* 修复表格右侧白色区域 */
:deep(.el-table) {
  background-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-bg-color: transparent;
  --el-table-border-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__inner-wrapper::before) {
  display: none;
}

:deep(.el-table__fixed-right) {
  background-color: rgba(10, 26, 58, 0.6);
  height: 100% !important;
}

:deep(.el-table__fixed-right-patch) {
  background-color: rgba(13, 34, 82, 0.8);
}

:deep(.el-table__cell) {
  background-color: transparent;
}

:deep(.el-table--border::after),
:deep(.el-table--border::before),
:deep(.el-table__inner-wrapper::before) {
  background-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__fixed::before),
:deep(.el-table__fixed-right::before) {
  background-color: transparent;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: calc(100vh - 120px); /* 减去头部和导航的高度 */
  color: $text-light;
  overflow-y: auto;
}

.page-title {
  font-size: 26px;
  font-weight: bold;
  color: $text-highlight;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
  margin-bottom: 20px;
  text-align: center;
  width: 100%;
}

.table-container {
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.table-panel, .query-panel, .chart-panel {
  background-color: rgba($secondary-dark-blue, 0.7);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 5px;
  box-shadow: 0 0 20px rgba($glow-blue, 0.2);
  padding: 15px;
}

.table-panel {
  width: 65%;
  height: 380px;
  display: flex;
  flex-direction: column;
}

.table-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  overflow: hidden;
  padding: 0 10px;
}

.query-panel {
  width: 35%;
  height: 380px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-title {
  color: $text-highlight;
  font-size: 18px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba($border-glow, 0.3);
  padding-bottom: 8px;
  text-align: center;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
}

.custom-table {
  --el-table-border-color: rgba(0, 136, 255, 0.3);
  --el-table-border: 1px solid var(--el-table-border-color);
  --el-table-text-color: #ffffff;
  --el-table-header-text-color: #00ffff;
  --el-table-row-hover-bg-color: rgba(0, 168, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  width: auto;
  margin: 0 auto;
  font-size: 14px;
}

.query-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

.input-row {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  gap: 10px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 48%;
}

.label {
  color: $text-light;
  font-size: 15px;
}

.custom-input {
  width: 100%;
  margin-top: 10px;
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-text-color: #ffffff;
  --el-input-border-color: rgba(0, 136, 255, 0.5);
  --el-input-hover-border-color: rgba(0, 168, 255, 0.8);
}

.query-btn {
  margin-top: 15px;
  background-color: $highlight-blue;
  border-color: $highlight-blue;
  color: $text-light;
  font-weight: bold;
  width: 100%;
  font-size: 15px;
  height: 36px;
  &:hover {
    background-color: darken($highlight-blue, 10%);
    border-color: darken($highlight-blue, 10%);
  }
}

.result-container {
  margin-top: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba($border-glow, 0.5);
  border-radius: 5px;
  padding: 8px;
}

.result-label {
  font-size: 15px;
  color: $text-highlight;
  margin-bottom: 3px;
  text-align: center;
}

.result-value {
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-statistic {
  --el-statistic-content-font-size: 32px;
  --el-statistic-content-color: $data-highlight;
}

.chart-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 20px;
  margin-bottom: 20px; /* 添加底部边距，防止内容超出边界 */
}

.chart-panel {
  width: 48%;
  height: 280px; /* 减小高度，防止超出边界 */
}

.com-container {
  width: 100%;
  height: 100%;
}

.com-chart1 {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// 适配深色模式下的Element Plus组件
:deep(.el-input-number) {
  --el-input-bg-color: rgba(10, 26, 58, 0.6);
  --el-input-text-color: #ffffff;
  --el-input-border-color: rgba(0, 136, 255, 0.5);
  --el-input-hover-border-color: rgba(0, 168, 255, 0.8);
  width: 100%;
  height: 32px;
  font-size: 14px;
}

:deep(.el-input__wrapper) {
  background-color: rgba(10, 26, 58, 0.6);
  box-shadow: 0 0 0 1px rgba(0, 136, 255, 0.5) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px rgba(0, 198, 255, 0.8) inset;
}

:deep(.el-input__inner) {
  color: #ffffff;
  background-color: transparent;
  font-size: 14px;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background-color: rgba(13, 34, 82, 0.8);
  color: #00ffff;
  border-color: rgba(0, 136, 255, 0.5);
}
</style>