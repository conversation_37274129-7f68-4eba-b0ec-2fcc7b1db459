<template>
  <div class="screen-container" :style="containerStyle">
    <header class="screen-header">
      <div class="header-content">
        <span class="logo">
          <img src="/static/img/长江电力.png" alt="Logo" />
        </span>
        <span class="title">实时动库容对三峡-葛洲坝梯级短期调度影响</span>
        <div class="title-right">
          <span class="system-version">系统版本</span>
          <span class="login-info" @click="handleLogout">退出登录</span>
          <span class="about">关于</span>
        </div>
      </div>
    </header>

    <!-- Navigation Tabs -->
    <div class="nav-tabs-container">
      <div class="nav-tabs">
        <div
          v-for="tab in navTabs"
          :key="tab.id"
          class="nav-tab"
          :class="{ active: tab.active }"
          @click="setActiveTab(tab.id)"
        >
          {{ tab.label }}
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="screen-body">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/userStore';

// 容器样式
const containerStyle = ref('')

// 获取路由
const router = useRouter();

// 用户Store
const userStore = useUserStore();

// 处理登出
const handleLogout = () => {
  // 清除用户信息
  userStore.clearUserInfo();
  // 跳转到登录页
  router.push('/login');
};

// Navigation tabs
const navTabs = ref([
  { id: 'home', label: '首页', active: false },
  { id: '1', label: '动库容计算', active: false },
  { id: '2', label: '三-葛传播时间', active: false },
  { id: '3', label: '小溪塔传播时间', active: false },
  { id: '4', label: '三-葛回水顶托', active: false },
  { id: '5', label: '葛洲坝回水顶托', active: false },
  { id: '6', label: '水文水动力模型计算', active: false },
  { id: '7', label: '优化调度计算', active: false },
])

// 根据当前路由路径设置活动标签
const updateActiveTab = () => {
  const path = window.location.pathname;
  const tabId = path === '/home' ? 'home' : path.substring(1);
  navTabs.value = navTabs.value.map(tab => ({
    ...tab,
    active: tab.id === tabId
  }));
}

// 页面加载时更新活动标签
updateActiveTab();

// Set active tab and navigate to the corresponding route
const setActiveTab = (tabId: string) => {
  navTabs.value = navTabs.value.map(tab => ({
    ...tab,
    active: tab.id === tabId
  }))

  // 处理路由跳转
  if (tabId === 'home') {
    router.push('/home');
  } else if (['1', '2', '3', '4', '5', '6', '7'].includes(tabId)) {
    router.push(`/${tabId}`);
  }
}
</script>

<style lang="scss" scoped>
// Main colors
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$map-highlight: #00a8ff;
$data-highlight: #00ffff;
$nav-active: #00a8ff;
$nav-inactive: #1e3c72;

// Global container
.screen-container {
  width: 100%;
  height: 100%;
  padding: 0;
  background-color: $primary-dark-blue;
  background-image: linear-gradient(to bottom, $secondary-dark-blue, $primary-dark-blue);
  color: $text-light;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('./static/img/grid-pattern.png');
    background-size: 20px 20px;
    opacity: 0.1;
    pointer-events: none;
    z-index: 1;
  }
}

// Header styles
.screen-header {
  width: 100%;
  height: 60px;
  font-size: 20px;
  position: relative;
  z-index: 10;
  border-bottom: 1px solid rgba($border-glow, 0.3);

  .header-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 20px;
    width: 100%;
    position: relative; /* 添加相对定位 */
  }

  .title {
    color: $text-light;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba($glow-blue, 0.5);
    letter-spacing: 2px;
    text-align: center;
    position: absolute; /* 使用绝对定位 */
    left: 50%; /* 左边缘在容器中心 */
    top: 50%; /* 上边缘在容器中心 */
    transform: translate(-50%, -50%); /* 向左上偏移自身宽高的一半，实现真正居中 */
    white-space: nowrap; /* 防止标题换行 */
    z-index: 1; /* 确保标题在其他元素之上 */
  }

  .title-right {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-left: auto; /* 将右侧元素推到最右边 */
    z-index: 2; /* 确保右侧元素在标题之上 */

    span {
      font-size: 14px;
      cursor: pointer;
      padding: 5px 10px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba($highlight-blue, 0.2);
      }
    }
  }

  .logo {
    display: flex;
    align-items: center;
    z-index: 2; /* 确保logo在标题之上 */

    img {
      height: 40px;
      width: auto;
    }
  }
}

// Navigation tabs
.nav-tabs-container {
  padding: 0 20px;
  margin-top: 5px;
  z-index: 10;
  position: relative;
  display: flex;
  justify-content: center; // 整体居中显示
}

.nav-tabs {
  display: flex;
  border-bottom: 1px solid rgba($border-glow, 0.2);
  justify-content: center; // 内容居中

  .nav-tab {
    padding: 8px 20px;
    margin-right: 2px;
    background-color: $nav-inactive;
    color: $text-light;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px; // 设置固定宽度
    text-align: center; // 文字居中

    &.active {
      background-color: $nav-active;
      color: $text-highlight;
      box-shadow: 0 0 15px rgba($glow-blue, 0.5);
    }

    &:hover:not(.active) {
      background-color: lighten($nav-inactive, 10%);
    }
  }
}

// Main content area
.screen-body {
  width: 100%;
  height: calc(100% - 110px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
}

// Responsive adjustments
@media (max-width: 1600px) {
  .nav-tabs .nav-tab {
    min-width: 100px;
    padding: 8px 15px;
    font-size: 14px;
  }

  .screen-header .title {
    font-size: 20px;
  }
}
</style>
